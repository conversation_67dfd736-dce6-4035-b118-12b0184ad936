#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
任务调度器

模块描述: 管理定时任务和异步任务执行
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
"""

import logging
import threading
import time
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass
from core import BaseService, ServiceResponse, ComponentConfig


class TaskStatus(Enum):
    """任务状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    SCHEDULED = "scheduled"


class TaskPriority(Enum):
    """任务优先级枚举"""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class ScheduledTask:
    """调度任务"""

    task_id: str
    name: str
    handler: Callable
    schedule_time: datetime
    priority: TaskPriority = TaskPriority.NORMAL
    retry_count: int = 3
    timeout: int = 300
    context: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.context is None:
            self.context = {}


@dataclass
class TaskResult:
    """任务执行结果"""

    task_id: str
    status: TaskStatus
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    result: Any = None
    error: Optional[str] = None
    retry_attempts: int = 0


class TaskScheduler(BaseService):
    """
    任务调度器

    负责管理定时任务、异步任务的调度和执行
    """

    def __init__(self, config: Optional[ComponentConfig] = None):
        if config is None:
            config = ComponentConfig(name="task_scheduler", version="1.0.0")
        super().__init__(config)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.tasks: Dict[str, ScheduledTask] = {}
        self.task_results: Dict[str, TaskResult] = {}
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.lock = threading.Lock()

    def start(self) -> ServiceResponse:
        """
        启动调度器

        Returns:
            ServiceResponse: 启动结果
        """
        try:
            if self.running:
                return ServiceResponse(success=False, error_message="调度器已经在运行")

            self.running = True
            self.scheduler_thread = threading.Thread(
                target=self._scheduler_loop, daemon=True
            )
            self.scheduler_thread.start()

            self.logger.info("任务调度器启动成功")
            return ServiceResponse(success=True, data={"status": "started"})

        except Exception as e:
            self.logger.error(f"启动调度器失败: {e}")
            return ServiceResponse(
                success=False, error_message=f"启动调度器失败: {str(e)}"
            )

    def stop(self) -> ServiceResponse:
        """
        停止调度器

        Returns:
            ServiceResponse: 停止结果
        """
        try:
            self.running = False
            if self.scheduler_thread:
                self.scheduler_thread.join(timeout=5)

            self.logger.info("任务调度器停止成功")
            return ServiceResponse(success=True, data={"status": "stopped"})

        except Exception as e:
            self.logger.error(f"停止调度器失败: {e}")
            return ServiceResponse(
                success=False, error_message=f"停止调度器失败: {str(e)}"
            )

    def schedule_task(self, task: ScheduledTask) -> ServiceResponse:
        """
        调度任务

        Args:
            task: 调度任务

        Returns:
            ServiceResponse: 调度结果
        """
        try:
            with self.lock:
                if task.task_id in self.tasks:
                    return ServiceResponse(
                        success=False, error_message=f"任务 {task.task_id} 已存在"
                    )

                self.tasks[task.task_id] = task
                self.task_results[task.task_id] = TaskResult(
                    task_id=task.task_id, status=TaskStatus.SCHEDULED
                )

            self.logger.info(
                f"任务 {task.task_id} 调度成功，执行时间: {task.schedule_time}"
            )
            return ServiceResponse(
                success=True,
                data={
                    "task_id": task.task_id,
                    "schedule_time": task.schedule_time.isoformat(),
                },
            )

        except Exception as e:
            self.logger.error(f"调度任务失败: {e}")
            return ServiceResponse(
                success=False, error_message=f"调度任务失败: {str(e)}"
            )

    def schedule_recurring_task(
        self,
        task_id: str,
        name: str,
        handler: Callable,
        interval_minutes: int,
        context: Optional[Dict[str, Any]] = None,
    ) -> ServiceResponse:
        """
        调度循环任务

        Args:
            task_id: 任务ID
            name: 任务名称
            handler: 处理函数
            interval_minutes: 间隔分钟数
            context: 执行上下文

        Returns:
            ServiceResponse: 调度结果
        """
        try:
            next_run = datetime.now() + timedelta(minutes=interval_minutes)
            task = ScheduledTask(
                task_id=task_id,
                name=name,
                handler=handler,
                schedule_time=next_run,
                context=context or {},
            )

            # 添加循环标记
            if task.context is None:
                task.context = {}
            task.context["_recurring"] = True
            task.context["_interval_minutes"] = interval_minutes

            return self.schedule_task(task)

        except Exception as e:
            self.logger.error(f"调度循环任务失败: {e}")
            return ServiceResponse(
                success=False, error_message=f"调度循环任务失败: {str(e)}"
            )

    def cancel_task(self, task_id: str) -> ServiceResponse:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            ServiceResponse: 取消结果
        """
        try:
            with self.lock:
                if task_id not in self.tasks:
                    return ServiceResponse(
                        success=False, error_message=f"任务 {task_id} 不存在"
                    )

                result = self.task_results[task_id]
                if result.status == TaskStatus.RUNNING:
                    return ServiceResponse(
                        success=False,
                        error_message=f"任务 {task_id} 正在运行，无法取消",
                    )

                del self.tasks[task_id]
                result.status = TaskStatus.CANCELLED

            self.logger.info(f"任务 {task_id} 取消成功")
            return ServiceResponse(
                success=True, data={"task_id": task_id, "status": "cancelled"}
            )

        except Exception as e:
            self.logger.error(f"取消任务失败: {e}")
            return ServiceResponse(
                success=False, error_message=f"取消任务失败: {str(e)}"
            )

    def get_task_status(self, task_id: str) -> ServiceResponse:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            ServiceResponse: 任务状态
        """
        try:
            if task_id not in self.task_results:
                return ServiceResponse(
                    success=False, error_message=f"任务 {task_id} 不存在"
                )

            result = self.task_results[task_id]
            return ServiceResponse(success=True, data=result.__dict__)

        except Exception as e:
            self.logger.error(f"获取任务状态失败: {e}")
            return ServiceResponse(
                success=False, error_message=f"获取任务状态失败: {str(e)}"
            )

    def list_tasks(self) -> ServiceResponse:
        """
        列出所有任务

        Returns:
            ServiceResponse: 任务列表
        """
        try:
            with self.lock:
                tasks = []
                for task_id, task in self.tasks.items():
                    result = self.task_results[task_id]
                    tasks.append(
                        {
                            "task_id": task_id,
                            "name": task.name,
                            "schedule_time": task.schedule_time.isoformat(),
                            "priority": task.priority.name,
                            "status": result.status.name,
                        }
                    )

            return ServiceResponse(success=True, data={"tasks": tasks})

        except Exception as e:
            self.logger.error(f"列出任务失败: {e}")
            return ServiceResponse(
                success=False, error_message=f"列出任务失败: {str(e)}"
            )

    def _scheduler_loop(self):
        """调度器主循环"""
        self.logger.info("调度器主循环启动")

        while self.running:
            try:
                current_time = datetime.now()
                ready_tasks = []

                with self.lock:
                    for task_id, task in list(self.tasks.items()):
                        if task.schedule_time <= current_time:
                            ready_tasks.append(task)
                            del self.tasks[task_id]

                # 执行准备好的任务
                for task in ready_tasks:
                    self._execute_task(task)

                # 休眠1秒
                time.sleep(1)

            except Exception as e:
                self.logger.error(f"调度器循环异常: {e}")
                time.sleep(5)

        self.logger.info("调度器主循环结束")

    def _execute_task(self, task: ScheduledTask):
        """执行任务"""

        def task_runner():
            result = self.task_results[task.task_id]
            result.status = TaskStatus.RUNNING
            result.start_time = datetime.now()

            try:
                self.logger.info(f"开始执行任务: {task.name}")
                task_result = task.handler(task.context)
                result.result = task_result
                result.status = TaskStatus.COMPLETED
                self.logger.info(f"任务 {task.name} 执行成功")

                # 处理循环任务
                if task.context and task.context.get("_recurring"):
                    interval = task.context.get("_interval_minutes", 60)
                    next_run = datetime.now() + timedelta(minutes=interval)
                    next_task = ScheduledTask(
                        task_id=task.task_id,
                        name=task.name,
                        handler=task.handler,
                        schedule_time=next_run,
                        priority=task.priority,
                        context=task.context,
                    )
                    self.schedule_task(next_task)

            except Exception as e:
                self.logger.error(f"任务 {task.name} 执行失败: {e}")
                result.error = str(e)
                result.status = TaskStatus.FAILED

            result.end_time = datetime.now()

        # 在新线程中执行任务
        thread = threading.Thread(target=task_runner, daemon=True)
        thread.start()
