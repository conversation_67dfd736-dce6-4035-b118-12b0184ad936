#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
核心功能实现状态检查

模块描述: 检查项目核心业务功能的实际实现状态
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, Any, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CoreFunctionalityChecker:
    """核心功能实现状态检查器"""
    
    def __init__(self):
        self.results = {
            "email_collection": {"status": "未检查", "details": {}, "completion": 0},
            "ai_analysis": {"status": "未检查", "details": {}, "completion": 0},
            "database_integration": {"status": "未检查", "details": {}, "completion": 0},
            "frontend_business": {"status": "未检查", "details": {}, "completion": 0},
            "api_services": {"status": "未检查", "details": {}, "completion": 0}
        }
    
    def check_email_collection(self):
        """检查邮件采集功能"""
        logger.info("检查邮件采集功能...")
        
        checks = []
        completion_score = 0
        
        # 1. 检查邮件连接模块
        try:
            from email_module.email_connection import EmailConnection
            checks.append(("邮件连接模块", "通过", "EmailConnection类可导入"))
            completion_score += 20
        except Exception as e:
            checks.append(("邮件连接模块", "失败", f"导入失败: {e}"))
        
        # 2. 检查邮件下载器
        if os.path.exists("email_downloader.py"):
            checks.append(("邮件下载器", "通过", "email_downloader.py存在"))
            completion_score += 20
        else:
            checks.append(("邮件下载器", "失败", "email_downloader.py不存在"))
        
        # 3. 检查邮件解析器
        try:
            from email_module.email_parser import EmailParser
            checks.append(("邮件解析器", "通过", "EmailParser类可导入"))
            completion_score += 20
        except Exception as e:
            checks.append(("邮件解析器", "失败", f"导入失败: {e}"))
        
        # 4. 检查附件下载功能
        try:
            from email_module.attachment_downloader import AttachmentDownloader
            checks.append(("附件下载器", "通过", "AttachmentDownloader类可导入"))
            completion_score += 20
        except Exception as e:
            checks.append(("附件下载器", "失败", f"导入失败: {e}"))
        
        # 5. 检查数据库保存功能
        try:
            # 检查是否有实际的数据库保存逻辑
            with open("email_downloader.py", "r", encoding="utf-8") as f:
                content = f.read()
                if "database" in content.lower() or "db" in content.lower():
                    checks.append(("数据库保存", "部分", "代码中包含数据库相关逻辑"))
                    completion_score += 10
                else:
                    checks.append(("数据库保存", "缺失", "未发现数据库保存逻辑"))
        except Exception as e:
            checks.append(("数据库保存", "错误", f"检查失败: {e}"))
        
        self.results["email_collection"]["details"] = checks
        self.results["email_collection"]["completion"] = completion_score
        self.results["email_collection"]["status"] = "通过" if completion_score >= 80 else "部分完成" if completion_score >= 50 else "失败"
        
        return completion_score >= 80
    
    def check_ai_analysis(self):
        """检查AI分析功能"""
        logger.info("检查AI分析功能...")
        
        checks = []
        completion_score = 0
        
        # 1. 检查AI适配器
        try:
            from ai.adapter import AIAdapter
            checks.append(("AI适配器", "通过", "AIAdapter类可导入"))
            completion_score += 25
        except Exception as e:
            checks.append(("AI适配器", "失败", f"导入失败: {e}"))
        
        # 2. 检查分析引擎
        try:
            from ai.analysis.analysis_engine import AnalysisEngine
            checks.append(("分析引擎", "通过", "AnalysisEngine类可导入"))
            completion_score += 25
        except Exception as e:
            checks.append(("分析引擎", "失败", f"导入失败: {e}"))
        
        # 3. 检查AI配置
        if os.path.exists("ai/ai_config.json"):
            checks.append(("AI配置", "通过", "ai_config.json存在"))
            completion_score += 15
        else:
            checks.append(("AI配置", "失败", "ai_config.json不存在"))
        
        # 4. 检查提示词模板
        if os.path.exists("prompt_templates") and len(os.listdir("prompt_templates")) > 0:
            template_count = len([f for f in os.listdir("prompt_templates") if f.endswith('.json')])
            checks.append(("提示词模板", "通过", f"存在{template_count}个模板文件"))
            completion_score += 15
        else:
            checks.append(("提示词模板", "失败", "提示词模板目录为空或不存在"))
        
        # 5. 检查实际AI调用逻辑
        try:
            # 检查是否有实际的AI模型调用
            with open("ai/adapter.py", "r", encoding="utf-8") as f:
                content = f.read()
                if "openai" in content.lower() and "chat" in content.lower():
                    checks.append(("AI模型调用", "通过", "包含OpenAI调用逻辑"))
                    completion_score += 20
                else:
                    checks.append(("AI模型调用", "部分", "AI调用逻辑不完整"))
                    completion_score += 10
        except Exception as e:
            checks.append(("AI模型调用", "错误", f"检查失败: {e}"))
        
        self.results["ai_analysis"]["details"] = checks
        self.results["ai_analysis"]["completion"] = completion_score
        self.results["ai_analysis"]["status"] = "通过" if completion_score >= 80 else "部分完成" if completion_score >= 50 else "失败"
        
        return completion_score >= 80
    
    def check_database_integration(self):
        """检查数据库集成功能"""
        logger.info("检查数据库集成功能...")
        
        checks = []
        completion_score = 0
        
        # 1. 检查数据库配置
        try:
            from config.base import DATABASE_CONFIG
            checks.append(("数据库配置", "通过", "DATABASE_CONFIG可导入"))
            completion_score += 20
        except Exception as e:
            checks.append(("数据库配置", "失败", f"导入失败: {e}"))
        
        # 2. 检查ORM模型
        try:
            from db.orm import Email, Employee, Report
            checks.append(("ORM模型", "通过", "主要实体模型可导入"))
            completion_score += 25
        except Exception as e:
            checks.append(("ORM模型", "失败", f"导入失败: {e}"))
        
        # 3. 检查数据库迁移脚本
        migration_dir = "db/migrations"
        if os.path.exists(migration_dir):
            migration_files = [f for f in os.listdir(migration_dir) if f.endswith('.sql')]
            if migration_files:
                checks.append(("数据库迁移", "通过", f"存在{len(migration_files)}个迁移文件"))
                completion_score += 20
            else:
                checks.append(("数据库迁移", "失败", "迁移目录存在但无SQL文件"))
        else:
            checks.append(("数据库迁移", "失败", "迁移目录不存在"))
        
        # 4. 检查数据库文件
        if os.path.exists("zkteco.db"):
            checks.append(("数据库文件", "通过", "zkteco.db存在"))
            completion_score += 15
        else:
            checks.append(("数据库文件", "失败", "zkteco.db不存在"))
        
        # 5. 检查数据仓储实现
        try:
            from domain.repositories import EmailRepository, EmployeeRepository
            checks.append(("数据仓储", "通过", "仓储接口可导入"))
            completion_score += 20
        except Exception as e:
            checks.append(("数据仓储", "失败", f"导入失败: {e}"))
        
        self.results["database_integration"]["details"] = checks
        self.results["database_integration"]["completion"] = completion_score
        self.results["database_integration"]["status"] = "通过" if completion_score >= 80 else "部分完成" if completion_score >= 50 else "失败"
        
        return completion_score >= 80
    
    def check_frontend_business(self):
        """检查前端业务功能"""
        logger.info("检查前端业务功能...")
        
        checks = []
        completion_score = 0
        
        # 1. 检查UI组件系统
        try:
            from components.pages import UnifiedDashboard, AnalysisWorkbench, InsightCenter
            checks.append(("UI组件系统", "通过", "页面组件可导入"))
            completion_score += 30
        except Exception as e:
            checks.append(("UI组件系统", "失败", f"导入失败: {e}"))
        
        # 2. 检查业务页面
        ui_pages_dir = "ui/pages"
        if os.path.exists(ui_pages_dir):
            page_files = [f for f in os.listdir(ui_pages_dir) if f.endswith('.py') and f != '__init__.py']
            if page_files:
                checks.append(("业务页面", "通过", f"存在{len(page_files)}个业务页面"))
                completion_score += 25
            else:
                checks.append(("业务页面", "失败", "业务页面目录为空"))
        else:
            checks.append(("业务页面", "失败", "业务页面目录不存在"))
        
        # 3. 检查主应用入口
        if os.path.exists("ui/app.py"):
            checks.append(("主应用入口", "通过", "ui/app.py存在"))
            completion_score += 15
        else:
            checks.append(("主应用入口", "失败", "ui/app.py不存在"))
        
        # 4. 检查数据集成
        try:
            # 检查UI页面是否有实际的数据集成
            with open("ui/app.py", "r", encoding="utf-8") as f:
                content = f.read()
                if "占位" in content or "placeholder" in content.lower():
                    checks.append(("数据集成", "缺失", "UI页面仍为占位符"))
                    completion_score += 5
                else:
                    checks.append(("数据集成", "通过", "UI页面有实际内容"))
                    completion_score += 30
        except Exception as e:
            checks.append(("数据集成", "错误", f"检查失败: {e}"))
        
        self.results["frontend_business"]["details"] = checks
        self.results["frontend_business"]["completion"] = completion_score
        self.results["frontend_business"]["status"] = "通过" if completion_score >= 80 else "部分完成" if completion_score >= 50 else "失败"
        
        return completion_score >= 80
    
    def check_api_services(self):
        """检查API服务功能"""
        logger.info("检查API服务功能...")
        
        checks = []
        completion_score = 0
        
        # 1. 检查FastAPI主应用
        try:
            from api.main import app
            checks.append(("FastAPI应用", "通过", "FastAPI应用可导入"))
            completion_score += 25
        except Exception as e:
            checks.append(("FastAPI应用", "失败", f"导入失败: {e}"))
        
        # 2. 检查API端点
        api_endpoints_dir = "api/endpoints"
        if os.path.exists(api_endpoints_dir):
            endpoint_files = [f for f in os.listdir(api_endpoints_dir) if f.endswith('.py')]
            if endpoint_files:
                checks.append(("API端点", "通过", f"存在{len(endpoint_files)}个端点文件"))
                completion_score += 25
            else:
                checks.append(("API端点", "失败", "端点目录为空"))
        else:
            checks.append(("API端点", "失败", "端点目录不存在"))
        
        # 3. 检查中间件
        middleware_dir = "api/middleware"
        if os.path.exists(middleware_dir):
            middleware_files = [f for f in os.listdir(middleware_dir) if f.endswith('.py') and f != '__init__.py']
            if middleware_files:
                checks.append(("API中间件", "通过", f"存在{len(middleware_files)}个中间件"))
                completion_score += 20
            else:
                checks.append(("API中间件", "失败", "中间件目录为空"))
        else:
            checks.append(("API中间件", "失败", "中间件目录不存在"))
        
        # 4. 检查服务层
        services_dir = "services"
        if os.path.exists(services_dir):
            service_subdirs = [d for d in os.listdir(services_dir) if os.path.isdir(os.path.join(services_dir, d))]
            if service_subdirs:
                checks.append(("服务层", "通过", f"存在{len(service_subdirs)}个服务模块"))
                completion_score += 30
            else:
                checks.append(("服务层", "失败", "服务目录为空"))
        else:
            checks.append(("服务层", "失败", "服务目录不存在"))
        
        self.results["api_services"]["details"] = checks
        self.results["api_services"]["completion"] = completion_score
        self.results["api_services"]["status"] = "通过" if completion_score >= 80 else "部分完成" if completion_score >= 50 else "失败"
        
        return completion_score >= 80
    
    def run_check(self):
        """运行完整检查"""
        print("🔍 开始核心功能实现状态检查...")
        print("=" * 60)
        
        # 执行各项检查
        email_ok = self.check_email_collection()
        ai_ok = self.check_ai_analysis()
        db_ok = self.check_database_integration()
        frontend_ok = self.check_frontend_business()
        api_ok = self.check_api_services()
        
        # 计算总体完成度
        total_completion = (
            self.results["email_collection"]["completion"] +
            self.results["ai_analysis"]["completion"] +
            self.results["database_integration"]["completion"] +
            self.results["frontend_business"]["completion"] +
            self.results["api_services"]["completion"]
        ) / 5
        
        # 生成报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "overall_completion": total_completion,
            "overall_status": "通过" if total_completion >= 80 else "部分完成" if total_completion >= 50 else "失败",
            "modules": self.results
        }
        
        # 保存报告
        with open("core_functionality_check_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        print("\n📊 核心功能实现状态摘要")
        print("-" * 60)
        print(f"邮件采集功能: {self.results['email_collection']['status']} ({self.results['email_collection']['completion']}%)")
        print(f"AI分析功能: {self.results['ai_analysis']['status']} ({self.results['ai_analysis']['completion']}%)")
        print(f"数据库集成: {self.results['database_integration']['status']} ({self.results['database_integration']['completion']}%)")
        print(f"前端业务功能: {self.results['frontend_business']['status']} ({self.results['frontend_business']['completion']}%)")
        print(f"API服务功能: {self.results['api_services']['status']} ({self.results['api_services']['completion']}%)")
        print("=" * 60)
        print(f"总体完成度: {total_completion:.1f}%")
        print(f"总体状态: {report['overall_status']}")
        
        if total_completion < 80:
            print("\n⚠️ 核心功能实现不完整，需要继续开发")
        else:
            print("\n✅ 核心功能基本完成，可以进入测试阶段")
        
        return total_completion >= 80

def main():
    """主函数"""
    checker = CoreFunctionalityChecker()
    success = checker.run_check()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
