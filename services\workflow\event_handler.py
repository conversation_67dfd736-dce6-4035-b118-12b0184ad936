#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
事件处理器

模块描述: 管理系统事件的发布和订阅
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
"""

import logging
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from enum import Enum
from dataclasses import dataclass
from core import BaseService, ServiceResponse


class EventType(Enum):
    """事件类型枚举"""
    EMAIL_DOWNLOADED = "email_downloaded"
    EMAIL_ANALYZED = "email_analyzed"
    ANALYSIS_COMPLETED = "analysis_completed"
    ERROR_OCCURRED = "error_occurred"
    TASK_STARTED = "task_started"
    TASK_COMPLETED = "task_completed"
    WORKFLOW_STARTED = "workflow_started"
    WORKFLOW_COMPLETED = "workflow_completed"
    SYSTEM_ALERT = "system_alert"


@dataclass
class Event:
    """事件数据结构"""
    event_type: EventType
    source: str
    timestamp: datetime
    data: Dict[str, Any] = None
    event_id: Optional[str] = None
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}
        if self.event_id is None:
            self.event_id = f"{self.event_type.value}_{self.timestamp.strftime('%Y%m%d_%H%M%S_%f')}"


@dataclass
class EventSubscription:
    """事件订阅"""
    subscriber_id: str
    event_type: EventType
    handler: Callable[[Event], None]
    filter_func: Optional[Callable[[Event], bool]] = None


class EventHandler(BaseService):
    """
    事件处理器
    
    负责管理系统事件的发布、订阅和分发
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.subscriptions: Dict[EventType, List[EventSubscription]] = {}
        self.event_history: List[Event] = []
        self.max_history_size = config.get('max_event_history', 1000) if config else 1000
        self.lock = threading.Lock()
        
    def subscribe(self, subscriber_id: str, event_type: EventType, 
                 handler: Callable[[Event], None],
                 filter_func: Optional[Callable[[Event], bool]] = None) -> ServiceResponse:
        """
        订阅事件
        
        Args:
            subscriber_id: 订阅者ID
            event_type: 事件类型
            handler: 事件处理函数
            filter_func: 事件过滤函数
            
        Returns:
            ServiceResponse: 订阅结果
        """
        try:
            with self.lock:
                if event_type not in self.subscriptions:
                    self.subscriptions[event_type] = []
                
                # 检查是否已经订阅
                for sub in self.subscriptions[event_type]:
                    if sub.subscriber_id == subscriber_id:
                        return ServiceResponse(
                            success=False,
                            message=f"订阅者 {subscriber_id} 已订阅事件 {event_type.value}"
                        )
                
                subscription = EventSubscription(
                    subscriber_id=subscriber_id,
                    event_type=event_type,
                    handler=handler,
                    filter_func=filter_func
                )
                
                self.subscriptions[event_type].append(subscription)
            
            self.logger.info(f"订阅者 {subscriber_id} 成功订阅事件 {event_type.value}")
            return ServiceResponse(
                success=True,
                message=f"成功订阅事件 {event_type.value}",
                data={"subscriber_id": subscriber_id, "event_type": event_type.value}
            )
            
        except Exception as e:
            self.logger.error(f"订阅事件失败: {e}")
            return ServiceResponse(
                success=False,
                message=f"订阅事件失败: {str(e)}"
            )
    
    def unsubscribe(self, subscriber_id: str, event_type: EventType) -> ServiceResponse:
        """
        取消订阅事件
        
        Args:
            subscriber_id: 订阅者ID
            event_type: 事件类型
            
        Returns:
            ServiceResponse: 取消订阅结果
        """
        try:
            with self.lock:
                if event_type not in self.subscriptions:
                    return ServiceResponse(
                        success=False,
                        message=f"事件类型 {event_type.value} 没有订阅者"
                    )
                
                # 查找并移除订阅
                subscriptions = self.subscriptions[event_type]
                for i, sub in enumerate(subscriptions):
                    if sub.subscriber_id == subscriber_id:
                        del subscriptions[i]
                        break
                else:
                    return ServiceResponse(
                        success=False,
                        message=f"订阅者 {subscriber_id} 未订阅事件 {event_type.value}"
                    )
                
                # 如果没有订阅者了，删除事件类型
                if not subscriptions:
                    del self.subscriptions[event_type]
            
            self.logger.info(f"订阅者 {subscriber_id} 成功取消订阅事件 {event_type.value}")
            return ServiceResponse(
                success=True,
                message=f"成功取消订阅事件 {event_type.value}"
            )
            
        except Exception as e:
            self.logger.error(f"取消订阅事件失败: {e}")
            return ServiceResponse(
                success=False,
                message=f"取消订阅事件失败: {str(e)}"
            )
    
    def publish(self, event_type: EventType, source: str, data: Dict[str, Any] = None) -> ServiceResponse:
        """
        发布事件
        
        Args:
            event_type: 事件类型
            source: 事件源
            data: 事件数据
            
        Returns:
            ServiceResponse: 发布结果
        """
        try:
            event = Event(
                event_type=event_type,
                source=source,
                timestamp=datetime.now(),
                data=data or {}
            )
            
            # 添加到历史记录
            with self.lock:
                self.event_history.append(event)
                # 限制历史记录大小
                if len(self.event_history) > self.max_history_size:
                    self.event_history = self.event_history[-self.max_history_size:]
                
                # 获取订阅者
                subscriptions = self.subscriptions.get(event_type, []).copy()
            
            # 分发事件给订阅者
            delivered_count = 0
            for subscription in subscriptions:
                try:
                    # 应用过滤器
                    if subscription.filter_func and not subscription.filter_func(event):
                        continue
                    
                    # 在新线程中处理事件，避免阻塞
                    def handle_event():
                        try:
                            subscription.handler(event)
                        except Exception as e:
                            self.logger.error(f"事件处理器 {subscription.subscriber_id} 处理事件失败: {e}")
                    
                    thread = threading.Thread(target=handle_event, daemon=True)
                    thread.start()
                    delivered_count += 1
                    
                except Exception as e:
                    self.logger.error(f"分发事件给订阅者 {subscription.subscriber_id} 失败: {e}")
            
            self.logger.info(f"事件 {event_type.value} 发布成功，分发给 {delivered_count} 个订阅者")
            return ServiceResponse(
                success=True,
                message=f"事件发布成功，分发给 {delivered_count} 个订阅者",
                data={
                    "event_id": event.event_id,
                    "event_type": event_type.value,
                    "delivered_count": delivered_count
                }
            )
            
        except Exception as e:
            self.logger.error(f"发布事件失败: {e}")
            return ServiceResponse(
                success=False,
                message=f"发布事件失败: {str(e)}"
            )
    
    def get_event_history(self, event_type: Optional[EventType] = None, 
                         limit: int = 100) -> ServiceResponse:
        """
        获取事件历史
        
        Args:
            event_type: 事件类型过滤器
            limit: 返回数量限制
            
        Returns:
            ServiceResponse: 事件历史
        """
        try:
            with self.lock:
                events = self.event_history.copy()
            
            # 过滤事件类型
            if event_type:
                events = [e for e in events if e.event_type == event_type]
            
            # 按时间倒序排列并限制数量
            events = sorted(events, key=lambda x: x.timestamp, reverse=True)[:limit]
            
            # 转换为字典格式
            event_data = []
            for event in events:
                event_data.append({
                    "event_id": event.event_id,
                    "event_type": event.event_type.value,
                    "source": event.source,
                    "timestamp": event.timestamp.isoformat(),
                    "data": event.data
                })
            
            return ServiceResponse(
                success=True,
                data={
                    "events": event_data,
                    "total_count": len(event_data)
                }
            )
            
        except Exception as e:
            self.logger.error(f"获取事件历史失败: {e}")
            return ServiceResponse(
                success=False,
                message=f"获取事件历史失败: {str(e)}"
            )
    
    def get_subscriptions(self) -> ServiceResponse:
        """
        获取所有订阅信息
        
        Returns:
            ServiceResponse: 订阅信息
        """
        try:
            with self.lock:
                subscription_data = {}
                for event_type, subscriptions in self.subscriptions.items():
                    subscription_data[event_type.value] = [
                        {
                            "subscriber_id": sub.subscriber_id,
                            "has_filter": sub.filter_func is not None
                        }
                        for sub in subscriptions
                    ]
            
            return ServiceResponse(
                success=True,
                data={"subscriptions": subscription_data}
            )
            
        except Exception as e:
            self.logger.error(f"获取订阅信息失败: {e}")
            return ServiceResponse(
                success=False,
                message=f"获取订阅信息失败: {str(e)}"
            )
    
    def clear_history(self) -> ServiceResponse:
        """
        清空事件历史
        
        Returns:
            ServiceResponse: 清空结果
        """
        try:
            with self.lock:
                cleared_count = len(self.event_history)
                self.event_history.clear()
            
            self.logger.info(f"清空了 {cleared_count} 条事件历史")
            return ServiceResponse(
                success=True,
                message=f"成功清空 {cleared_count} 条事件历史",
                data={"cleared_count": cleared_count}
            )
            
        except Exception as e:
            self.logger.error(f"清空事件历史失败: {e}")
            return ServiceResponse(
                success=False,
                message=f"清空事件历史失败: {str(e)}"
            )
