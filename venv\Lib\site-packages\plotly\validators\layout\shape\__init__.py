import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._ysizemode.YsizemodeValidator",
        "._yref.YrefValidator",
        "._yanchor.YanchorValidator",
        "._y1shift.Y1ShiftValidator",
        "._y1.Y1Validator",
        "._y0shift.Y0ShiftValidator",
        "._y0.Y0Validator",
        "._xsizemode.XsizemodeValidator",
        "._xref.XrefValidator",
        "._xanchor.XanchorValidator",
        "._x1shift.X1ShiftValidator",
        "._x1.X1Validator",
        "._x0shift.X0ShiftValidator",
        "._x0.X0Validator",
        "._visible.VisibleValidator",
        "._type.TypeValidator",
        "._templateitemname.TemplateitemnameValidator",
        "._showlegend.ShowlegendValidator",
        "._path.PathValidator",
        "._opacity.OpacityValidator",
        "._name.NameValidator",
        "._line.LineValidator",
        "._legendwidth.LegendwidthValidator",
        "._legendrank.LegendrankValidator",
        "._legendgrouptitle.LegendgrouptitleValidator",
        "._legendgroup.LegendgroupValidator",
        "._legend.LegendValidator",
        "._layer.LayerValidator",
        "._label.LabelValidator",
        "._fillrule.FillruleValidator",
        "._fillcolor.FillcolorValidator",
        "._editable.EditableValidator",
    ],
)
