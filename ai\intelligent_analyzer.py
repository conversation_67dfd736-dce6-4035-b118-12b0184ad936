#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
智能分析器 - 确保正确使用大模型和向量模型

模块描述: 智能分析器，根据任务类型自动选择合适的模型（LLM或向量模型）
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ai.adapter, typing, json, logging
"""

import json
import logging
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

from .adapter import AIAdapter, ModelType

logger = logging.getLogger(__name__)

class AnalysisTask(Enum):
    """分析任务类型"""
    TEXT_ANALYSIS = "text_analysis"  # 文本分析 - 使用LLM
    CONTENT_GENERATION = "content_generation"  # 内容生成 - 使用LLM
    SIMILARITY_SEARCH = "similarity_search"  # 相似度搜索 - 使用向量模型
    CLUSTERING = "clustering"  # 聚类分析 - 使用向量模型
    CLASSIFICATION = "classification"  # 分类任务 - 使用LLM
    SUMMARIZATION = "summarization"  # 摘要生成 - 使用LLM
    EMBEDDING_EXTRACTION = "embedding_extraction"  # 向量提取 - 使用向量模型
    SEMANTIC_SEARCH = "semantic_search"  # 语义搜索 - 使用向量模型

@dataclass
class AnalysisResult:
    """分析结果"""
    task_type: AnalysisTask
    model_type: ModelType
    model_name: str
    result: Union[str, List[float], Dict[str, Any]]
    confidence: float
    processing_time: float
    metadata: Dict[str, Any]

class IntelligentAnalyzer:
    """智能分析器 - 根据任务自动选择合适的模型"""
    
    def __init__(self, config_path: str = None):
        """
        初始化智能分析器
        
        Args:
            config_path: AI配置文件路径
        """
        self.ai_adapter = AIAdapter(config_path)
        self.task_model_mapping = {
            # 需要使用大语言模型的任务
            AnalysisTask.TEXT_ANALYSIS: ModelType.LLM,
            AnalysisTask.CONTENT_GENERATION: ModelType.LLM,
            AnalysisTask.CLASSIFICATION: ModelType.LLM,
            AnalysisTask.SUMMARIZATION: ModelType.LLM,
            
            # 需要使用向量模型的任务
            AnalysisTask.SIMILARITY_SEARCH: ModelType.EMBEDDING,
            AnalysisTask.CLUSTERING: ModelType.EMBEDDING,
            AnalysisTask.EMBEDDING_EXTRACTION: ModelType.EMBEDDING,
            AnalysisTask.SEMANTIC_SEARCH: ModelType.EMBEDDING,
        }
    
    def analyze_email_content(self, email_content: str, analysis_type: str = "comprehensive") -> AnalysisResult:
        """
        分析邮件内容 - 使用LLM进行文本分析
        
        Args:
            email_content: 邮件内容
            analysis_type: 分析类型 (comprehensive, quick, detailed)
            
        Returns:
            AnalysisResult: 分析结果
        """
        import time
        start_time = time.time()
        
        # 构建分析提示词
        prompt = self._build_email_analysis_prompt(email_content, analysis_type)
        
        # 使用LLM进行分析
        result = self.ai_adapter.call(
            prompt=prompt,
            model_type=ModelType.LLM,
            strategy="best"
        )
        
        processing_time = time.time() - start_time
        
        # 解析结果
        try:
            parsed_result = json.loads(result)
            confidence = parsed_result.get("confidence", 0.8)
        except:
            parsed_result = {"raw_result": result, "confidence": 0.6}
            confidence = 0.6
        
        return AnalysisResult(
            task_type=AnalysisTask.TEXT_ANALYSIS,
            model_type=ModelType.LLM,
            model_name="LLM",
            result=parsed_result,
            confidence=confidence,
            processing_time=processing_time,
            metadata={"analysis_type": analysis_type}
        )
    
    def extract_email_embedding(self, email_content: str) -> AnalysisResult:
        """
        提取邮件向量表示 - 使用向量模型
        
        Args:
            email_content: 邮件内容
            
        Returns:
            AnalysisResult: 向量结果
        """
        import time
        start_time = time.time()
        
        # 使用向量模型提取向量
        embedding = self.ai_adapter.call_embedding(text=email_content)
        
        processing_time = time.time() - start_time
        
        return AnalysisResult(
            task_type=AnalysisTask.EMBEDDING_EXTRACTION,
            model_type=ModelType.EMBEDDING,
            model_name="Embedding",
            result=embedding,
            confidence=0.9,
            processing_time=processing_time,
            metadata={"vector_dimension": len(embedding)}
        )
    
    def find_similar_emails(self, query_embedding: List[float], 
                           email_embeddings: List[Dict[str, Any]], 
                           top_k: int = 5) -> AnalysisResult:
        """
        查找相似邮件 - 基于向量相似度
        
        Args:
            query_embedding: 查询向量
            email_embeddings: 邮件向量数据库
            top_k: 返回前K个相似结果
            
        Returns:
            AnalysisResult: 相似度搜索结果
        """
        import time
        import numpy as np
        
        start_time = time.time()
        
        # 计算余弦相似度
        similarities = []
        for email_data in email_embeddings:
            email_embedding = email_data.get("embedding", [])
            if email_embedding:
                similarity = self._cosine_similarity(query_embedding, email_embedding)
                similarities.append({
                    "email_id": email_data.get("email_id"),
                    "similarity": similarity,
                    "metadata": email_data.get("metadata", {})
                })
        
        # 排序并返回前K个
        similarities.sort(key=lambda x: x["similarity"], reverse=True)
        top_results = similarities[:top_k]
        
        processing_time = time.time() - start_time
        
        return AnalysisResult(
            task_type=AnalysisTask.SIMILARITY_SEARCH,
            model_type=ModelType.EMBEDDING,
            model_name="Similarity",
            result=top_results,
            confidence=0.85,
            processing_time=processing_time,
            metadata={"total_compared": len(email_embeddings), "top_k": top_k}
        )
    
    def generate_email_summary(self, email_content: str, summary_type: str = "brief") -> AnalysisResult:
        """
        生成邮件摘要 - 使用LLM
        
        Args:
            email_content: 邮件内容
            summary_type: 摘要类型 (brief, detailed, bullet_points)
            
        Returns:
            AnalysisResult: 摘要结果
        """
        import time
        start_time = time.time()
        
        # 构建摘要提示词
        prompt = self._build_summary_prompt(email_content, summary_type)
        
        # 使用LLM生成摘要
        result = self.ai_adapter.call(
            prompt=prompt,
            model_type=ModelType.LLM,
            strategy="best"
        )
        
        processing_time = time.time() - start_time
        
        return AnalysisResult(
            task_type=AnalysisTask.SUMMARIZATION,
            model_type=ModelType.LLM,
            model_name="LLM",
            result=result,
            confidence=0.8,
            processing_time=processing_time,
            metadata={"summary_type": summary_type}
        )
    
    def classify_email_type(self, email_content: str) -> AnalysisResult:
        """
        分类邮件类型 - 使用LLM
        
        Args:
            email_content: 邮件内容
            
        Returns:
            AnalysisResult: 分类结果
        """
        import time
        start_time = time.time()
        
        # 构建分类提示词
        prompt = self._build_classification_prompt(email_content)
        
        # 使用LLM进行分类
        result = self.ai_adapter.call(
            prompt=prompt,
            model_type=ModelType.LLM,
            strategy="round_robin"  # 轮询使用不同模型
        )
        
        processing_time = time.time() - start_time
        
        # 解析分类结果
        try:
            parsed_result = json.loads(result)
            confidence = parsed_result.get("confidence", 0.7)
        except:
            parsed_result = {"category": "unknown", "confidence": 0.5, "raw": result}
            confidence = 0.5
        
        return AnalysisResult(
            task_type=AnalysisTask.CLASSIFICATION,
            model_type=ModelType.LLM,
            model_name="LLM",
            result=parsed_result,
            confidence=confidence,
            processing_time=processing_time,
            metadata={}
        )
    
    def batch_analyze_emails(self, emails: List[str], 
                           include_embeddings: bool = True) -> List[AnalysisResult]:
        """
        批量分析邮件 - 同时使用LLM和向量模型
        
        Args:
            emails: 邮件内容列表
            include_embeddings: 是否包含向量提取
            
        Returns:
            List[AnalysisResult]: 批量分析结果
        """
        results = []
        
        for i, email_content in enumerate(emails):
            logger.info(f"分析邮件 {i+1}/{len(emails)}")
            
            # 文本分析（使用LLM）
            text_analysis = self.analyze_email_content(email_content, "quick")
            results.append(text_analysis)
            
            # 向量提取（使用向量模型）
            if include_embeddings:
                embedding_result = self.extract_email_embedding(email_content)
                results.append(embedding_result)
            
            # 分类（使用LLM）
            classification = self.classify_email_type(email_content)
            results.append(classification)
        
        return results
    
    def _build_email_analysis_prompt(self, email_content: str, analysis_type: str) -> str:
        """构建邮件分析提示词"""
        if analysis_type == "comprehensive":
            return f"""
请对以下邮件内容进行全面分析，返回JSON格式结果：

邮件内容：
{email_content}

请分析以下方面：
1. 邮件类型（工作报告、技术支持、销售等）
2. 关键信息提取
3. 情感倾向
4. 重要程度评级
5. 需要的后续行动

返回格式：
{{
    "email_type": "类型",
    "key_points": ["要点1", "要点2"],
    "sentiment": "positive/neutral/negative",
    "importance": "high/medium/low",
    "next_actions": ["行动1", "行动2"],
    "confidence": 0.8
}}
"""
        else:
            return f"""
请简要分析以下邮件内容，返回JSON格式：

{email_content}

返回格式：
{{
    "email_type": "类型",
    "summary": "简要总结",
    "confidence": 0.8
}}
"""
    
    def _build_summary_prompt(self, email_content: str, summary_type: str) -> str:
        """构建摘要提示词"""
        if summary_type == "bullet_points":
            return f"请将以下邮件内容总结为要点列表：\n\n{email_content}"
        elif summary_type == "detailed":
            return f"请详细总结以下邮件内容，包含主要观点和细节：\n\n{email_content}"
        else:
            return f"请简要总结以下邮件内容：\n\n{email_content}"
    
    def _build_classification_prompt(self, email_content: str) -> str:
        """构建分类提示词"""
        return f"""
请对以下邮件进行分类，返回JSON格式：

{email_content}

可能的类别：
- work_report: 工作报告
- technical_support: 技术支持
- sales_inquiry: 销售咨询
- meeting_notice: 会议通知
- project_update: 项目更新
- other: 其他

返回格式：
{{
    "category": "分类结果",
    "confidence": 0.8,
    "reasoning": "分类理由"
}}
"""
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        import math
        
        # 计算点积
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        
        # 计算向量长度
        magnitude1 = math.sqrt(sum(a * a for a in vec1))
        magnitude2 = math.sqrt(sum(a * a for a in vec2))
        
        # 避免除零
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)
    
    def get_model_usage_stats(self) -> Dict[str, Any]:
        """获取模型使用统计"""
        return self.ai_adapter.get_stats()
