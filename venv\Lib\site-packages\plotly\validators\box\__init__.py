import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._zorder.ZorderValidator",
        "._ysrc.YsrcValidator",
        "._yperiodalignment.YperiodalignmentValidator",
        "._yperiod0.Yperiod0Validator",
        "._yperiod.YperiodValidator",
        "._yhoverformat.YhoverformatValidator",
        "._ycalendar.YcalendarValidator",
        "._yaxis.YaxisValidator",
        "._y0.Y0Validator",
        "._y.YValidator",
        "._xsrc.XsrcValidator",
        "._xperiodalignment.XperiodalignmentValidator",
        "._xperiod0.Xperiod0Validator",
        "._xperiod.XperiodValidator",
        "._xhoverformat.XhoverformatValidator",
        "._xcalendar.XcalendarValidator",
        "._xaxis.XaxisValidator",
        "._x0.X0Validator",
        "._x.XValidator",
        "._width.WidthValidator",
        "._whiskerwidth.WhiskerwidthValidator",
        "._visible.VisibleValidator",
        "._upperfencesrc.UpperfencesrcValidator",
        "._upperfence.UpperfenceValidator",
        "._unselected.UnselectedValidator",
        "._uirevision.UirevisionValidator",
        "._uid.UidValidator",
        "._textsrc.TextsrcValidator",
        "._text.TextValidator",
        "._stream.StreamValidator",
        "._sizemode.SizemodeValidator",
        "._showwhiskers.ShowwhiskersValidator",
        "._showlegend.ShowlegendValidator",
        "._selectedpoints.SelectedpointsValidator",
        "._selected.SelectedValidator",
        "._sdsrc.SdsrcValidator",
        "._sdmultiple.SdmultipleValidator",
        "._sd.SdValidator",
        "._quartilemethod.QuartilemethodValidator",
        "._q3src.Q3SrcValidator",
        "._q3.Q3Validator",
        "._q1src.Q1SrcValidator",
        "._q1.Q1Validator",
        "._pointpos.PointposValidator",
        "._orientation.OrientationValidator",
        "._opacity.OpacityValidator",
        "._offsetgroup.OffsetgroupValidator",
        "._notchwidth.NotchwidthValidator",
        "._notchspansrc.NotchspansrcValidator",
        "._notchspan.NotchspanValidator",
        "._notched.NotchedValidator",
        "._name.NameValidator",
        "._metasrc.MetasrcValidator",
        "._meta.MetaValidator",
        "._mediansrc.MediansrcValidator",
        "._median.MedianValidator",
        "._meansrc.MeansrcValidator",
        "._mean.MeanValidator",
        "._marker.MarkerValidator",
        "._lowerfencesrc.LowerfencesrcValidator",
        "._lowerfence.LowerfenceValidator",
        "._line.LineValidator",
        "._legendwidth.LegendwidthValidator",
        "._legendrank.LegendrankValidator",
        "._legendgrouptitle.LegendgrouptitleValidator",
        "._legendgroup.LegendgroupValidator",
        "._legend.LegendValidator",
        "._jitter.JitterValidator",
        "._idssrc.IdssrcValidator",
        "._ids.IdsValidator",
        "._hovertextsrc.HovertextsrcValidator",
        "._hovertext.HovertextValidator",
        "._hovertemplatesrc.HovertemplatesrcValidator",
        "._hovertemplate.HovertemplateValidator",
        "._hoveron.HoveronValidator",
        "._hoverlabel.HoverlabelValidator",
        "._hoverinfosrc.HoverinfosrcValidator",
        "._hoverinfo.HoverinfoValidator",
        "._fillcolor.FillcolorValidator",
        "._dy.DyValidator",
        "._dx.DxValidator",
        "._customdatasrc.CustomdatasrcValidator",
        "._customdata.CustomdataValidator",
        "._boxpoints.BoxpointsValidator",
        "._boxmean.BoxmeanValidator",
        "._alignmentgroup.AlignmentgroupValidator",
    ],
)
