#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合报告展示组件 - 多方面报告呈现

模块描述: 提供多维度、多方面的报告展示界面，确保数据分析结果清晰呈现
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: streamlit, plotly, pandas
"""

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
from typing import Dict, Any, List
from datetime import datetime

from services.analysis.comprehensive_analyzer import ComprehensiveReport, EmailAnalysisReport

class ComprehensiveReportViewer:
    """综合报告展示器"""
    
    def __init__(self):
        self.colors = {
            "primary": "#1f77b4",
            "secondary": "#ff7f0e", 
            "success": "#2ca02c",
            "warning": "#d62728",
            "info": "#9467bd"
        }
    
    def render_report(self, report: ComprehensiveReport):
        """渲染完整报告"""
        st.title("📊 AI驱动邮件分析综合报告")
        
        # 报告基本信息
        self._render_report_header(report)
        
        # 创建标签页
        tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
            "📈 统计概览", "📊 详细分析", "🔍 趋势洞察", 
            "⚠️ 异常检测", "📋 个人报告", "🔧 技术指标"
        ])
        
        with tab1:
            self._render_statistics_overview(report)
        
        with tab2:
            self._render_detailed_analysis(report)
        
        with tab3:
            self._render_trend_insights(report)
        
        with tab4:
            self._render_anomaly_detection(report)
        
        with tab5:
            self._render_individual_reports(report)
        
        with tab6:
            self._render_technical_metrics(report)
    
    def _render_report_header(self, report: ComprehensiveReport):
        """渲染报告头部信息"""
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="📧 总邮件数",
                value=report.total_emails,
                delta=None
            )
        
        with col2:
            st.metric(
                label="⏱️ 总工作时间",
                value=f"{report.total_work_hours:.1f}h",
                delta=f"平均 {report.average_work_hours:.1f}h"
            )
        
        with col3:
            st.metric(
                label="📊 工作效率",
                value=f"{report.work_efficiency_score:.1f}%",
                delta="效率评分"
            )
        
        with col4:
            period_days = (report.analysis_period[1] - report.analysis_period[0]).days + 1
            st.metric(
                label="📅 分析周期",
                value=f"{period_days}天",
                delta=f"{report.analysis_period[0].strftime('%m/%d')} - {report.analysis_period[1].strftime('%m/%d')}"
            )
        
        st.divider()
    
    def _render_statistics_overview(self, report: ComprehensiveReport):
        """渲染统计概览"""
        st.subheader("📈 统计概览")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 邮件类型分布饼图
            if report.email_type_distribution:
                fig_type = px.pie(
                    values=list(report.email_type_distribution.values()),
                    names=list(report.email_type_distribution.keys()),
                    title="邮件类型分布",
                    color_discrete_sequence=px.colors.qualitative.Set3
                )
                fig_type.update_traces(textposition='inside', textinfo='percent+label')
                st.plotly_chart(fig_type, use_container_width=True)
        
        with col2:
            # 情感分布柱状图
            if report.sentiment_distribution:
                sentiment_df = pd.DataFrame([
                    {"情感": k, "数量": v} for k, v in report.sentiment_distribution.items()
                ])
                fig_sentiment = px.bar(
                    sentiment_df, x="情感", y="数量",
                    title="情感倾向分布",
                    color="情感",
                    color_discrete_map={
                        "positive": self.colors["success"],
                        "neutral": self.colors["info"],
                        "negative": self.colors["warning"]
                    }
                )
                st.plotly_chart(fig_sentiment, use_container_width=True)
        
        # 重要程度分布
        if report.importance_distribution:
            st.subheader("重要程度分布")
            importance_df = pd.DataFrame([
                {"重要程度": k, "数量": v} for k, v in report.importance_distribution.items()
            ])
            fig_importance = px.bar(
                importance_df, x="重要程度", y="数量",
                title="邮件重要程度分布",
                color="重要程度",
                color_discrete_map={
                    "high": self.colors["warning"],
                    "medium": self.colors["primary"],
                    "low": self.colors["info"]
                }
            )
            st.plotly_chart(fig_importance, use_container_width=True)
    
    def _render_detailed_analysis(self, report: ComprehensiveReport):
        """渲染详细分析"""
        st.subheader("📊 详细分析")
        
        # 工作时间分析
        st.subheader("⏱️ 工作时间分析")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("总工作时间", f"{report.total_work_hours:.1f} 小时")
        
        with col2:
            st.metric("平均工作时间", f"{report.average_work_hours:.1f} 小时")
        
        with col3:
            st.metric("工作效率评分", f"{report.work_efficiency_score:.1f}%")
        
        # 工作时间分布直方图
        if report.individual_reports:
            work_hours = [r.work_hours for r in report.individual_reports]
            fig_hours = px.histogram(
                x=work_hours,
                nbins=20,
                title="工作时间分布",
                labels={"x": "工作时间(小时)", "y": "邮件数量"}
            )
            fig_hours.update_layout(showlegend=False)
            st.plotly_chart(fig_hours, use_container_width=True)
        
        # 邮件类型详细统计
        st.subheader("📧 邮件类型详细统计")
        if report.email_type_distribution:
            type_df = pd.DataFrame([
                {
                    "邮件类型": k,
                    "数量": v,
                    "占比": f"{v/report.total_emails*100:.1f}%"
                }
                for k, v in report.email_type_distribution.items()
            ])
            st.dataframe(type_df, use_container_width=True)
    
    def _render_trend_insights(self, report: ComprehensiveReport):
        """渲染趋势洞察"""
        st.subheader("🔍 趋势洞察")
        
        # 每日邮件数量趋势
        if report.daily_email_count:
            st.subheader("📅 每日邮件数量趋势")
            daily_df = pd.DataFrame([
                {"日期": k, "邮件数量": v} for k, v in report.daily_email_count.items()
            ])
            daily_df["日期"] = pd.to_datetime(daily_df["日期"])
            daily_df = daily_df.sort_values("日期")
            
            fig_daily = px.line(
                daily_df, x="日期", y="邮件数量",
                title="每日邮件数量变化",
                markers=True
            )
            fig_daily.update_layout(xaxis_title="日期", yaxis_title="邮件数量")
            st.plotly_chart(fig_daily, use_container_width=True)
        
        # 周趋势分析
        if report.weekly_trends:
            st.subheader("📊 周趋势分析")
            weekly_df = pd.DataFrame([
                {"周": k, "平均工作时间": v} for k, v in report.weekly_trends.items()
            ])
            
            fig_weekly = px.bar(
                weekly_df, x="周", y="平均工作时间",
                title="各周平均工作时间",
                color="平均工作时间",
                color_continuous_scale="Blues"
            )
            st.plotly_chart(fig_weekly, use_container_width=True)
        
        # 工作效率趋势
        if report.individual_reports:
            st.subheader("📈 工作效率趋势")
            efficiency_data = []
            for report_item in report.individual_reports:
                efficiency_data.append({
                    "日期": report_item.date.strftime("%Y-%m-%d"),
                    "工作时间": report_item.work_hours,
                    "任务数量": len(report_item.tasks),
                    "置信度": report_item.confidence_score
                })
            
            if efficiency_data:
                eff_df = pd.DataFrame(efficiency_data)
                eff_df["日期"] = pd.to_datetime(eff_df["日期"])
                eff_df = eff_df.sort_values("日期")
                
                # 创建多轴图表
                fig_eff = make_subplots(
                    rows=2, cols=1,
                    subplot_titles=("工作时间与任务数量", "分析置信度"),
                    vertical_spacing=0.1
                )
                
                # 工作时间
                fig_eff.add_trace(
                    go.Scatter(x=eff_df["日期"], y=eff_df["工作时间"], 
                             name="工作时间", line=dict(color=self.colors["primary"])),
                    row=1, col=1
                )
                
                # 任务数量
                fig_eff.add_trace(
                    go.Scatter(x=eff_df["日期"], y=eff_df["任务数量"], 
                             name="任务数量", line=dict(color=self.colors["secondary"])),
                    row=1, col=1
                )
                
                # 置信度
                fig_eff.add_trace(
                    go.Scatter(x=eff_df["日期"], y=eff_df["置信度"], 
                             name="置信度", line=dict(color=self.colors["success"])),
                    row=2, col=1
                )
                
                fig_eff.update_layout(height=600, title_text="工作效率趋势分析")
                st.plotly_chart(fig_eff, use_container_width=True)
    
    def _render_anomaly_detection(self, report: ComprehensiveReport):
        """渲染异常检测"""
        st.subheader("⚠️ 异常检测")
        
        if not report.detected_anomalies:
            st.success("🎉 未检测到异常情况")
            return
        
        # 异常统计
        anomaly_types = {}
        for anomaly in report.detected_anomalies:
            atype = anomaly.get("type", "unknown")
            anomaly_types[atype] = anomaly_types.get(atype, 0) + 1
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric("检测到的异常", len(report.detected_anomalies))
        
        with col2:
            st.metric("异常类型", len(anomaly_types))
        
        # 异常类型分布
        if anomaly_types:
            anomaly_df = pd.DataFrame([
                {"异常类型": k, "数量": v} for k, v in anomaly_types.items()
            ])
            fig_anomaly = px.bar(
                anomaly_df, x="异常类型", y="数量",
                title="异常类型分布",
                color="数量",
                color_continuous_scale="Reds"
            )
            st.plotly_chart(fig_anomaly, use_container_width=True)
        
        # 异常详情
        st.subheader("异常详情")
        for i, anomaly in enumerate(report.detected_anomalies):
            severity = anomaly.get("severity", "medium")
            severity_color = {
                "high": "🔴",
                "medium": "🟡", 
                "low": "🟢"
            }.get(severity, "⚪")
            
            with st.expander(f"{severity_color} {anomaly.get('type', '未知异常')} - {anomaly.get('email_id', '')}"):
                st.write(f"**描述**: {anomaly.get('description', '无描述')}")
                st.write(f"**严重程度**: {severity}")
                st.write(f"**邮件ID**: {anomaly.get('email_id', '无')}")
    
    def _render_individual_reports(self, report: ComprehensiveReport):
        """渲染个人报告"""
        st.subheader("📋 个人邮件分析报告")
        
        if not report.individual_reports:
            st.info("暂无个人报告数据")
            return
        
        # 筛选选项
        col1, col2, col3 = st.columns(3)
        
        with col1:
            email_types = list(set(r.email_type for r in report.individual_reports))
            selected_type = st.selectbox("邮件类型", ["全部"] + email_types)
        
        with col2:
            sentiments = list(set(r.sentiment for r in report.individual_reports))
            selected_sentiment = st.selectbox("情感倾向", ["全部"] + sentiments)
        
        with col3:
            importance_levels = list(set(r.importance for r in report.individual_reports))
            selected_importance = st.selectbox("重要程度", ["全部"] + importance_levels)
        
        # 筛选报告
        filtered_reports = report.individual_reports
        if selected_type != "全部":
            filtered_reports = [r for r in filtered_reports if r.email_type == selected_type]
        if selected_sentiment != "全部":
            filtered_reports = [r for r in filtered_reports if r.sentiment == selected_sentiment]
        if selected_importance != "全部":
            filtered_reports = [r for r in filtered_reports if r.importance == selected_importance]
        
        st.write(f"显示 {len(filtered_reports)} / {len(report.individual_reports)} 封邮件")
        
        # 显示报告
        for i, email_report in enumerate(filtered_reports[:10]):  # 限制显示前10个
            with st.expander(f"📧 {email_report.subject[:50]}... - {email_report.sender}"):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write(f"**发件人**: {email_report.sender}")
                    st.write(f"**日期**: {email_report.date.strftime('%Y-%m-%d %H:%M')}")
                    st.write(f"**类型**: {email_report.email_type}")
                    st.write(f"**情感**: {email_report.sentiment}")
                    st.write(f"**重要性**: {email_report.importance}")
                
                with col2:
                    st.write(f"**工作时间**: {email_report.work_hours:.1f} 小时")
                    st.write(f"**任务数量**: {len(email_report.tasks)}")
                    st.write(f"**置信度**: {email_report.confidence_score:.2f}")
                    st.write(f"**处理时间**: {email_report.processing_time:.2f} 秒")
                
                if email_report.content_summary:
                    st.write("**内容摘要**:")
                    st.write(email_report.content_summary)
                
                if email_report.key_points:
                    st.write("**关键要点**:")
                    for point in email_report.key_points:
                        st.write(f"• {point}")
                
                if email_report.tags:
                    st.write("**标签**:")
                    st.write(" ".join([f"`{tag}`" for tag in email_report.tags]))
                
                if email_report.anomalies:
                    st.write("**异常**:")
                    for anomaly in email_report.anomalies:
                        st.warning(f"⚠️ {anomaly}")
    
    def _render_technical_metrics(self, report: ComprehensiveReport):
        """渲染技术指标"""
        st.subheader("🔧 技术指标")
        
        # 处理性能
        st.subheader("⚡ 处理性能")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            total_time = report.processing_metadata.get("processing_time", 0)
            st.metric("总处理时间", f"{total_time:.2f} 秒")
        
        with col2:
            avg_time = total_time / max(report.total_emails, 1)
            st.metric("平均处理时间", f"{avg_time:.2f} 秒/邮件")
        
        with col3:
            throughput = report.total_emails / max(total_time, 1) * 60
            st.metric("处理吞吐量", f"{throughput:.1f} 邮件/分钟")
        
        # 模型使用统计
        if report.model_usage_stats:
            st.subheader("🤖 模型使用统计")
            
            adapter_stats = report.model_usage_stats.get("adapter_stats", {})
            if adapter_stats:
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("总请求数", adapter_stats.get("total_requests", 0))
                
                with col2:
                    st.metric("成功请求", adapter_stats.get("successful_requests", 0))
                
                with col3:
                    st.metric("失败请求", adapter_stats.get("failed_requests", 0))
                
                with col4:
                    success_rate = adapter_stats.get("success_rate", 0)
                    st.metric("成功率", f"{success_rate:.1f}%")
            
            # 模型详细统计
            model_stats = report.model_usage_stats.get("model_stats", {})
            if model_stats:
                st.subheader("模型详细统计")
                
                for model_type, stats in model_stats.items():
                    with st.expander(f"{model_type.upper()} 模型统计"):
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            st.metric("总模型数", stats.get("total", 0))
                            st.metric("可用模型", stats.get("available", 0))
                        
                        with col2:
                            models = stats.get("models", [])
                            if models:
                                model_df = pd.DataFrame(models)
                                st.dataframe(model_df, use_container_width=True)
        
        # 系统信息
        st.subheader("ℹ️ 系统信息")
        metadata = report.processing_metadata
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**分析器版本**: {metadata.get('analyzer_version', '未知')}")
            st.write(f"**分析时间**: {metadata.get('analysis_date', '未知')}")
        
        with col2:
            st.write(f"**报告ID**: {report.report_id}")
            st.write(f"**生成时间**: {report.generation_time.strftime('%Y-%m-%d %H:%M:%S')}")
