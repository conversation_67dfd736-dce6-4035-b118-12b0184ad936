import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._visible.VisibleValidator",
        "._uirevision.UirevisionValidator",
        "._subunitwidth.SubunitwidthValidator",
        "._subunitcolor.SubunitcolorValidator",
        "._showsubunits.ShowsubunitsValidator",
        "._showrivers.ShowriversValidator",
        "._showocean.ShowoceanValidator",
        "._showland.ShowlandValidator",
        "._showlakes.ShowlakesValidator",
        "._showframe.ShowframeValidator",
        "._showcountries.ShowcountriesValidator",
        "._showcoastlines.ShowcoastlinesValidator",
        "._scope.ScopeValidator",
        "._riverwidth.RiverwidthValidator",
        "._rivercolor.RivercolorValidator",
        "._resolution.ResolutionValidator",
        "._projection.ProjectionValidator",
        "._oceancolor.OceancolorValidator",
        "._lonaxis.LonaxisValidator",
        "._lataxis.LataxisValidator",
        "._landcolor.LandcolorValidator",
        "._lakecolor.LakecolorValidator",
        "._framewidth.FramewidthValidator",
        "._framecolor.FramecolorValidator",
        "._fitbounds.FitboundsValidator",
        "._domain.DomainValidator",
        "._countrywidth.CountrywidthValidator",
        "._countrycolor.CountrycolorValidator",
        "._coastlinewidth.CoastlinewidthValidator",
        "._coastlinecolor.CoastlinecolorValidator",
        "._center.CenterValidator",
        "._bgcolor.BgcolorValidator",
    ],
)
