#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合分析服务 - 数据分析和结果展示

模块描述: 提供全面的数据分析功能，确保结果展示清晰，支持多方面报告呈现
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: ai.intelligent_analyzer, typing, pandas, numpy
"""

import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from ai.intelligent_analyzer import IntelligentAnalyzer, AnalysisResult, AnalysisTask

logger = logging.getLogger(__name__)

@dataclass
class EmailAnalysisReport:
    """邮件分析报告"""
    email_id: str
    sender: str
    subject: str
    date: datetime
    content_summary: str
    email_type: str
    key_points: List[str]
    sentiment: str
    importance: str
    work_hours: float
    tasks: List[Dict[str, Any]]
    tags: List[str]
    anomalies: List[str]
    confidence_score: float
    processing_time: float

@dataclass
class ComprehensiveReport:
    """综合分析报告"""
    report_id: str
    generation_time: datetime
    analysis_period: Tuple[datetime, datetime]
    total_emails: int
    
    # 统计摘要
    email_type_distribution: Dict[str, int]
    sentiment_distribution: Dict[str, int]
    importance_distribution: Dict[str, int]
    
    # 工作分析
    total_work_hours: float
    average_work_hours: float
    work_efficiency_score: float
    
    # 趋势分析
    daily_email_count: Dict[str, int]
    weekly_trends: Dict[str, float]
    
    # 异常检测
    detected_anomalies: List[Dict[str, Any]]
    
    # 详细报告
    individual_reports: List[EmailAnalysisReport]
    
    # 元数据
    model_usage_stats: Dict[str, Any]
    processing_metadata: Dict[str, Any]

class ComprehensiveAnalyzer:
    """综合分析器 - 提供全面的数据分析和报告生成"""
    
    def __init__(self, config_path: str = None):
        """
        初始化综合分析器
        
        Args:
            config_path: AI配置文件路径
        """
        self.intelligent_analyzer = IntelligentAnalyzer(config_path)
        self.analysis_cache = {}
        
    def analyze_email_batch(self, emails_data: List[Dict[str, Any]]) -> ComprehensiveReport:
        """
        批量分析邮件并生成综合报告
        
        Args:
            emails_data: 邮件数据列表，每个包含 id, sender, subject, date, content
            
        Returns:
            ComprehensiveReport: 综合分析报告
        """
        start_time = datetime.now()
        logger.info(f"开始批量分析 {len(emails_data)} 封邮件")
        
        # 1. 逐个分析邮件
        individual_reports = []
        for email_data in emails_data:
            report = self._analyze_single_email(email_data)
            individual_reports.append(report)
        
        # 2. 生成统计摘要
        stats = self._generate_statistics(individual_reports)
        
        # 3. 趋势分析
        trends = self._analyze_trends(individual_reports)
        
        # 4. 异常检测
        anomalies = self._detect_anomalies(individual_reports)
        
        # 5. 计算分析期间
        dates = [report.date for report in individual_reports]
        analysis_period = (min(dates), max(dates)) if dates else (start_time, start_time)
        
        # 6. 获取模型使用统计
        model_stats = self.intelligent_analyzer.get_model_usage_stats()
        
        # 7. 生成综合报告
        comprehensive_report = ComprehensiveReport(
            report_id=f"report_{start_time.strftime('%Y%m%d_%H%M%S')}",
            generation_time=start_time,
            analysis_period=analysis_period,
            total_emails=len(emails_data),
            
            # 统计摘要
            email_type_distribution=stats["email_types"],
            sentiment_distribution=stats["sentiments"],
            importance_distribution=stats["importance"],
            
            # 工作分析
            total_work_hours=stats["total_hours"],
            average_work_hours=stats["avg_hours"],
            work_efficiency_score=stats["efficiency_score"],
            
            # 趋势分析
            daily_email_count=trends["daily_counts"],
            weekly_trends=trends["weekly_trends"],
            
            # 异常检测
            detected_anomalies=anomalies,
            
            # 详细报告
            individual_reports=individual_reports,
            
            # 元数据
            model_usage_stats=model_stats,
            processing_metadata={
                "processing_time": (datetime.now() - start_time).total_seconds(),
                "analyzer_version": "1.0.0",
                "analysis_date": start_time.isoformat()
            }
        )
        
        logger.info(f"批量分析完成，耗时 {comprehensive_report.processing_metadata['processing_time']:.2f} 秒")
        return comprehensive_report
    
    def _analyze_single_email(self, email_data: Dict[str, Any]) -> EmailAnalysisReport:
        """分析单封邮件"""
        email_content = email_data.get("content", "")
        
        # 使用智能分析器进行多维度分析
        # 1. 内容分析（使用LLM）
        content_analysis = self.intelligent_analyzer.analyze_email_content(
            email_content, "comprehensive"
        )
        
        # 2. 分类（使用LLM）
        classification = self.intelligent_analyzer.classify_email_type(email_content)
        
        # 3. 摘要生成（使用LLM）
        summary = self.intelligent_analyzer.generate_email_summary(email_content, "brief")
        
        # 4. 向量提取（使用向量模型）
        embedding = self.intelligent_analyzer.extract_email_embedding(email_content)
        
        # 解析分析结果
        content_result = content_analysis.result if isinstance(content_analysis.result, dict) else {}
        classification_result = classification.result if isinstance(classification.result, dict) else {}
        
        # 提取关键信息
        email_type = classification_result.get("category", "unknown")
        key_points = content_result.get("key_points", [])
        sentiment = content_result.get("sentiment", "neutral")
        importance = content_result.get("importance", "medium")
        
        # 工作时间估算
        work_hours = self._estimate_work_hours(email_content, email_type)
        
        # 任务提取
        tasks = self._extract_tasks(email_content)
        
        # 标签生成
        tags = self._generate_tags(content_result, classification_result)
        
        # 异常检测
        anomalies = self._detect_email_anomalies(email_data, content_result)
        
        # 计算置信度
        confidence_score = (
            content_analysis.confidence + 
            classification.confidence
        ) / 2
        
        return EmailAnalysisReport(
            email_id=email_data.get("id", ""),
            sender=email_data.get("sender", ""),
            subject=email_data.get("subject", ""),
            date=email_data.get("date", datetime.now()),
            content_summary=summary.result if isinstance(summary.result, str) else "",
            email_type=email_type,
            key_points=key_points,
            sentiment=sentiment,
            importance=importance,
            work_hours=work_hours,
            tasks=tasks,
            tags=tags,
            anomalies=anomalies,
            confidence_score=confidence_score,
            processing_time=content_analysis.processing_time + classification.processing_time
        )
    
    def _generate_statistics(self, reports: List[EmailAnalysisReport]) -> Dict[str, Any]:
        """生成统计摘要"""
        if not reports:
            return {}
        
        # 邮件类型分布
        email_types = {}
        sentiments = {}
        importance = {}
        
        total_hours = 0
        
        for report in reports:
            # 统计邮件类型
            email_types[report.email_type] = email_types.get(report.email_type, 0) + 1
            
            # 统计情感倾向
            sentiments[report.sentiment] = sentiments.get(report.sentiment, 0) + 1
            
            # 统计重要程度
            importance[report.importance] = importance.get(report.importance, 0) + 1
            
            # 累计工作时间
            total_hours += report.work_hours
        
        avg_hours = total_hours / len(reports) if reports else 0
        
        # 计算工作效率分数（基于多个因素）
        efficiency_score = self._calculate_efficiency_score(reports)
        
        return {
            "email_types": email_types,
            "sentiments": sentiments,
            "importance": importance,
            "total_hours": total_hours,
            "avg_hours": avg_hours,
            "efficiency_score": efficiency_score
        }
    
    def _analyze_trends(self, reports: List[EmailAnalysisReport]) -> Dict[str, Any]:
        """分析趋势"""
        if not reports:
            return {"daily_counts": {}, "weekly_trends": {}}
        
        # 按日期统计邮件数量
        daily_counts = {}
        for report in reports:
            date_str = report.date.strftime("%Y-%m-%d")
            daily_counts[date_str] = daily_counts.get(date_str, 0) + 1
        
        # 计算周趋势
        weekly_trends = {}
        df = pd.DataFrame([(r.date, r.work_hours) for r in reports], 
                         columns=["date", "hours"])
        if not df.empty:
            df["week"] = df["date"].dt.isocalendar().week
            weekly_avg = df.groupby("week")["hours"].mean().to_dict()
            weekly_trends = {f"week_{k}": v for k, v in weekly_avg.items()}
        
        return {
            "daily_counts": daily_counts,
            "weekly_trends": weekly_trends
        }
    
    def _detect_anomalies(self, reports: List[EmailAnalysisReport]) -> List[Dict[str, Any]]:
        """检测异常"""
        anomalies = []
        
        if not reports:
            return anomalies
        
        # 工作时间异常
        work_hours = [r.work_hours for r in reports]
        if work_hours:
            mean_hours = np.mean(work_hours)
            std_hours = np.std(work_hours)
            
            for report in reports:
                if abs(report.work_hours - mean_hours) > 2 * std_hours:
                    anomalies.append({
                        "type": "work_hours_anomaly",
                        "email_id": report.email_id,
                        "description": f"工作时间异常: {report.work_hours:.1f}小时 (平均: {mean_hours:.1f})",
                        "severity": "medium"
                    })
        
        # 置信度异常
        low_confidence_reports = [r for r in reports if r.confidence_score < 0.6]
        for report in low_confidence_reports:
            anomalies.append({
                "type": "low_confidence",
                "email_id": report.email_id,
                "description": f"分析置信度较低: {report.confidence_score:.2f}",
                "severity": "low"
            })
        
        return anomalies
    
    def _estimate_work_hours(self, content: str, email_type: str) -> float:
        """估算工作时间"""
        # 基于内容长度和邮件类型的简单估算
        base_hours = len(content) / 1000  # 每1000字符约1小时
        
        # 根据邮件类型调整
        type_multipliers = {
            "work_report": 1.5,
            "technical_support": 1.2,
            "project_update": 1.3,
            "meeting_notice": 0.5,
            "sales_inquiry": 0.8,
            "other": 1.0
        }
        
        multiplier = type_multipliers.get(email_type, 1.0)
        return round(base_hours * multiplier, 1)
    
    def _extract_tasks(self, content: str) -> List[Dict[str, Any]]:
        """提取任务"""
        # 简单的任务提取逻辑
        tasks = []
        
        # 查找任务关键词
        task_keywords = ["完成", "处理", "解决", "开发", "测试", "部署", "修复"]
        
        sentences = content.split("。")
        for i, sentence in enumerate(sentences):
            for keyword in task_keywords:
                if keyword in sentence:
                    tasks.append({
                        "id": f"task_{i}",
                        "description": sentence.strip(),
                        "type": "extracted",
                        "priority": "medium"
                    })
                    break
        
        return tasks[:5]  # 最多返回5个任务
    
    def _generate_tags(self, content_result: Dict, classification_result: Dict) -> List[str]:
        """生成标签"""
        tags = []
        
        # 基于邮件类型的标签
        email_type = classification_result.get("category", "")
        if email_type:
            tags.append(f"类型:{email_type}")
        
        # 基于情感的标签
        sentiment = content_result.get("sentiment", "")
        if sentiment:
            tags.append(f"情感:{sentiment}")
        
        # 基于重要程度的标签
        importance = content_result.get("importance", "")
        if importance:
            tags.append(f"重要性:{importance}")
        
        return tags
    
    def _detect_email_anomalies(self, email_data: Dict, content_result: Dict) -> List[str]:
        """检测邮件异常"""
        anomalies = []
        
        # 检查邮件长度异常
        content = email_data.get("content", "")
        if len(content) < 10:
            anomalies.append("内容过短")
        elif len(content) > 10000:
            anomalies.append("内容过长")
        
        # 检查置信度异常
        confidence = content_result.get("confidence", 1.0)
        if confidence < 0.5:
            anomalies.append("分析置信度低")
        
        return anomalies
    
    def _calculate_efficiency_score(self, reports: List[EmailAnalysisReport]) -> float:
        """计算工作效率分数"""
        if not reports:
            return 0.0
        
        # 基于多个因素计算效率分数
        factors = []
        
        # 1. 平均置信度
        avg_confidence = np.mean([r.confidence_score for r in reports])
        factors.append(avg_confidence)
        
        # 2. 任务完成度（基于任务数量）
        avg_tasks = np.mean([len(r.tasks) for r in reports])
        task_score = min(avg_tasks / 3, 1.0)  # 假设3个任务为满分
        factors.append(task_score)
        
        # 3. 工作时间合理性
        work_hours = [r.work_hours for r in reports]
        if work_hours:
            # 理想工作时间范围 4-10小时
            reasonable_hours = [h for h in work_hours if 4 <= h <= 10]
            time_score = len(reasonable_hours) / len(work_hours)
            factors.append(time_score)
        
        return round(np.mean(factors) * 100, 1)  # 转换为百分制
    
    def export_report_to_dict(self, report: ComprehensiveReport) -> Dict[str, Any]:
        """导出报告为字典格式"""
        return asdict(report)
    
    def export_report_to_json(self, report: ComprehensiveReport, 
                            filename: str = None) -> str:
        """导出报告为JSON格式"""
        report_dict = self.export_report_to_dict(report)
        
        # 处理datetime对象
        def json_serializer(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
        
        json_str = json.dumps(report_dict, ensure_ascii=False, indent=2, 
                            default=json_serializer)
        
        if filename:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(json_str)
            logger.info(f"报告已导出到: {filename}")
        
        return json_str
