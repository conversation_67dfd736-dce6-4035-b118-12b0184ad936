Metadata-Version: 2.1
Name: grafana-api
Version: 1.0.3
Summary: Yet another Python library for Grafana API
Home-page: https://github.com/m0nhawk/grafana_api/
Author: <PERSON>
Author-email: andrew.pro<PERSON><EMAIL>
License: MIT
Project-URL: Source, https://github.com/m0nhawk/grafana_api/
Project-URL: Tracker, https://github.com/m0nhawk/grafana_api/issues
Keywords: grafana api
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Topic :: Internet
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.6
Description-Content-Type: text/markdown
Requires-Dist: requests (>=2.23.0)
Provides-Extra: test
Requires-Dist: codecov (>=2.1.0) ; extra == 'test'
Requires-Dist: coverage (>=5.2.0) ; extra == 'test'
Requires-Dist: unittest-xml-reporting (>=3.0.0) ; extra == 'test'
Requires-Dist: requests-mock (>=1.8.0) ; extra == 'test'

# grafana_api [![Github Actions Test](https://github.com/m0nhawk/grafana_api/workflows/Test/badge.svg)](https://github.com/m0nhawk/grafana_api/actions?query=workflow%3ATest) [![GitHub license](https://img.shields.io/github/license/m0nhawk/grafana_api.svg?style=flat-square)](https://github.com/m0nhawk/grafana_api/blob/master/LICENSE)  [![Codecov](https://img.shields.io/codecov/c/gh/m0nhawk/grafana_api.svg?style=flat-square)](https://codecov.io/gh/m0nhawk/grafana_api/)

[![PyPI](https://img.shields.io/pypi/v/grafana_api.svg?style=flat-square)](https://pypi.org/project/grafana-api/) [![Conda](https://img.shields.io/conda/v/m0nhawk/grafana_api.svg?style=flat-square)](https://anaconda.org/m0nhawk/grafana_api)

## What is this library for?

Yet another Grafana API library for Python. Support Python 3 only.

## Requirements

You need Python 3 and only the `requests` library installed.

## Quick start

Install the pip package:

```
pip install -U grafana_api
```

And then connect to your Grafana API endpoint:

```python
from grafana_api.grafana_face import GrafanaFace

grafana_api = GrafanaFace(auth='abcde....', host='api.my-grafana-host.com')

# Search dashboards based on tag
grafana_api.search.search_dashboards(tag='applications')

# Find a user by email
user = grafana_api.users.find_user('<EMAIL>')

# Add user to team 2
grafana_api.teams.add_team_member(2, user["id"])

# Create or update a dashboard
grafana_api.dashboard.update_dashboard(dashboard={'dashboard': {...}, 'folderId': 0, 'overwrite': True})

# Delete a dashboard by UID
grafana_api.dashboard.delete_dashboard(dashboard_uid='abcdefgh')
```


## Authentication

There are two ways to autheticate to grafana api. Either use api token or basic auth.

To use admin API you need to use basic auth [as stated here](https://grafana.com/docs/grafana/latest/http_api/admin/)

```python
# Use basic authentication:

grafana_api = GrafanaFace(
          auth=("username","password"),
          host='api.my-grafana-host.com'
          )

# Use token
grafana_api = GrafanaFace(
          auth='abcdetoken...',
          host='api.my-grafana-host.com'
          )
```


## Status of REST API realization

Work on API implementation still in progress.

| API | Status |
|---|---|
| Admin | + |
| Alerting | - |
| Alerting Notification Channels | + |
| Annotations | + |
| Authentication | +- |
| Dashboard | + |
| Dashboard Versions | - |
| Dashboard Permissions | + |
| Data Source | + |
| Folder | + |
| Folder Permissions | + |
| Folder/Dashboard Search | +- |
| Organisation | + |
| Other | + |
| Preferences | + |
| Snapshot | + |
| Teams | + |
| User | + |

## Issue tracker

Please report any bugs and enhancement ideas using the `grafana_api` issue tracker:

  https://github.com/m0nhawk/grafana_api/issues

Feel free to also ask questions on the tracker.

## License

`grafana_api` is licensed under the terms of the MIT License (see the file
[LICENSE](LICENSE)).


