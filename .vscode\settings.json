{
    "python.defaultInterpreter": "./venv/Scripts/python.exe",
    "python.pythonPath": "./venv/Scripts/python.exe",
    "python.terminal.activateEnvironment": true,
    "python.terminal.activateEnvInCurrentTerminal": true,
    "python.analysis.autoImportCompletions": true,
    "python.analysis.diagnosticMode": "workspace",
    "pylance.insidersChannel": "off",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": false,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": [
        "--line-length=88"
    ],
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/venv/Lib/**": true,
        "**/venv/Scripts/**": true,
        "**/venv/Include/**": true,
        "**/venv/share/**": true,
        "**/venv/etc/**": true
    },
}