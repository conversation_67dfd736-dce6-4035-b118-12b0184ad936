import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._visible.VisibleValidator",
        "._unselected.UnselectedValidator",
        "._uirevision.UirevisionValidator",
        "._uid.UidValidator",
        "._thetaunit.ThetaunitValidator",
        "._thetasrc.ThetasrcValidator",
        "._theta0.Theta0Validator",
        "._theta.ThetaValidator",
        "._texttemplatesrc.TexttemplatesrcValidator",
        "._texttemplate.TexttemplateValidator",
        "._textsrc.TextsrcValidator",
        "._textpositionsrc.TextpositionsrcValidator",
        "._textposition.TextpositionValidator",
        "._textfont.TextfontValidator",
        "._text.TextValidator",
        "._subplot.SubplotValidator",
        "._stream.StreamValidator",
        "._showlegend.ShowlegendValidator",
        "._selectedpoints.SelectedpointsValidator",
        "._selected.SelectedValidator",
        "._rsrc.RsrcValidator",
        "._r0.R0Validator",
        "._r.RValidator",
        "._opacity.OpacityValidator",
        "._name.NameValidator",
        "._mode.ModeValidator",
        "._metasrc.MetasrcValidator",
        "._meta.MetaValidator",
        "._marker.MarkerValidator",
        "._line.LineValidator",
        "._legendwidth.LegendwidthValidator",
        "._legendrank.LegendrankValidator",
        "._legendgrouptitle.LegendgrouptitleValidator",
        "._legendgroup.LegendgroupValidator",
        "._legend.LegendValidator",
        "._idssrc.IdssrcValidator",
        "._ids.IdsValidator",
        "._hovertextsrc.HovertextsrcValidator",
        "._hovertext.HovertextValidator",
        "._hovertemplatesrc.HovertemplatesrcValidator",
        "._hovertemplate.HovertemplateValidator",
        "._hoverlabel.HoverlabelValidator",
        "._hoverinfosrc.HoverinfosrcValidator",
        "._hoverinfo.HoverinfoValidator",
        "._fillcolor.FillcolorValidator",
        "._fill.FillValidator",
        "._dtheta.DthetaValidator",
        "._dr.DrValidator",
        "._customdatasrc.CustomdatasrcValidator",
        "._customdata.CustomdataValidator",
        "._connectgaps.ConnectgapsValidator",
    ],
)
