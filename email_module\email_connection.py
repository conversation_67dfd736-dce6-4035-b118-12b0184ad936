#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
邮件连接模块 - 增强版
支持智能重连、连接池管理、指数退避重试策略
"""
import imaplib
import email
import datetime
import logging
import time
import random
from typing import List, Dict, Any, Optional
from threading import Lock
import queue


class EmailConnection:
    """增强版邮件连接类 - 支持智能重连和连接池"""

    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.config = config
        self.server = config.get("imap_server", "")
        self.port = config.get("port", 993)
        self.username = config.get("username", "")
        self.password = config.get("password", "")
        self.use_ssl = config.get("use_ssl", True)
        self.connected = False
        self.imap = None

        # 重连配置
        self.max_retries = config.get("max_retries", 5)
        self.base_delay = config.get("base_delay", 1)
        self.max_delay = config.get("max_delay", 60)
        self.retry_count = 0

        # 连接健康检查
        self.last_activity = time.time()
        self.health_check_interval = config.get("health_check_interval", 300)  # 5分钟

        # 连接锁
        self._lock = Lock()

    def _calculate_delay(self) -> float:
        """计算指数退避延迟时间"""
        delay = self.base_delay * (2**self.retry_count)
        # 添加随机抖动，避免雷群效应
        jitter = random.uniform(0.1, 0.3) * delay
        delay = min(delay + jitter, self.max_delay)
        return delay

    def _is_connection_healthy(self) -> bool:
        """检查连接健康状态"""
        if not self.connected or not self.imap:
            return False

        try:
            # 发送NOOP命令检查连接
            status, _ = self.imap.noop()
            if status == "OK":
                self.last_activity = time.time()
                return True
        except Exception as e:
            self.logger.warning(f"连接健康检查失败: {e}")

        return False

    def connect(self) -> bool:
        """智能连接方法，支持自动重连和健康检查"""
        with self._lock:
            # 检查现有连接是否健康
            if self.connected and self._is_connection_healthy():
                return True

            # 如果连接不健康，先断开
            if self.connected:
                self.disconnect()

            # 尝试重新连接
            for attempt in range(self.max_retries + 1):
                try:
                    self.logger.info(
                        f"尝试连接邮件服务器 (第{attempt + 1}次): {self.server}:{self.port}"
                    )

                    if self.use_ssl:
                        self.imap = imaplib.IMAP4_SSL(self.server, self.port)
                    else:
                        self.imap = imaplib.IMAP4(self.server, self.port)

                    self.imap.login(self.username, self.password)
                    self.connected = True
                    self.retry_count = 0  # 重置重试计数
                    self.last_activity = time.time()

                    self.logger.info(f"成功连接到邮件服务器: {self.server}:{self.port}")
                    return True

                except Exception as e:
                    self.logger.error(
                        f"连接邮件服务器失败 (第{attempt + 1}次): {str(e)}"
                    )
                    self.connected = False
                    self.imap = None

                    if attempt < self.max_retries:
                        delay = self._calculate_delay()
                        self.logger.info(f"等待 {delay:.2f} 秒后重试...")
                        time.sleep(delay)
                        self.retry_count += 1

            self.logger.error(f"连接邮件服务器失败，已尝试 {self.max_retries + 1} 次")
            return False

    def disconnect(self) -> None:
        if self.imap:
            try:
                self.imap.logout()
                self.logger.info("已断开与邮件服务器的连接")
            except Exception as e:
                self.logger.error(f"断开连接失败: {str(e)}")
            finally:
                self.imap = None
                self.connected = False

    def search_emails(
        self,
        criteria: Optional[str] = None,
        folder_name: str = "INBOX",
        max_emails: int = 0,
        start_date: Optional[datetime.date] = None,
        end_date: Optional[datetime.date] = None,
        sender: Optional[str] = None,
    ) -> List[str]:
        if not self.connected and not self.connect():
            self.logger.error("无法连接到邮件服务器")
            return []
        try:
            self.imap.select(folder_name)

            # 如果提供了criteria参数，直接使用
            if criteria:
                status, data = self.imap.search(None, criteria)
            else:
                # 否则根据其他参数构建搜索条件
                search_criteria = []
                if start_date:
                    search_criteria.append(f'SINCE {start_date.strftime("%d-%b-%Y")}')
                if end_date:
                    search_criteria.append(
                        f'BEFORE {(end_date + datetime.timedelta(days=1)).strftime("%d-%b-%Y")}'
                    )
                if sender:
                    search_criteria.append(f'FROM "{sender}"')
                if search_criteria:
                    search_str = " ".join(search_criteria)
                    status, data = self.imap.search(None, search_str)
                else:
                    status, data = self.imap.search(None, "ALL")
            if status != "OK":
                self.logger.error(f"搜索邮件失败: {status}")
                return []
            email_ids = data[0].split()
            if max_emails > 0 and len(email_ids) > max_emails:
                email_ids = email_ids[-max_emails:]
            email_ids = [email_id.decode("utf-8") for email_id in email_ids]
            self.logger.info(f"找到 {len(email_ids)} 封邮件")
            return email_ids
        except Exception as e:
            self.logger.error(f"搜索邮件失败: {str(e)}")
            return []

    def fetch_email(self, email_id: str, folder_name: str = "INBOX") -> Optional[bytes]:
        if not self.connected and not self.connect():
            self.logger.error("无法连接到邮件服务器")
            return None
        try:
            self.imap.select(folder_name)
            status, data = self.imap.fetch(email_id, "(RFC822)")
            if status != "OK":
                self.logger.error(f"获取邮件内容失败: {status}")
                return None
            return data[0][1]
        except Exception as e:
            self.logger.error(f"获取邮件内容失败: {str(e)}")
            return None

    def select_mailbox(self, mailbox: str = "INBOX") -> bool:
        """选择邮箱"""
        if not self.connected and not self.connect():
            self.logger.error("无法连接到邮件服务器")
            return False
        try:
            status, _ = self.imap.select(mailbox)
            if status == "OK":
                self.logger.info(f"成功选择邮箱: {mailbox}")
                return True
            else:
                self.logger.error(f"选择邮箱失败: {mailbox}, 状态: {status}")
                return False
        except Exception as e:
            self.logger.error(f"选择邮箱失败: {str(e)}")
            return False

    def get_email(self, email_id: str) -> Optional[Dict[str, Any]]:
        """获取邮件内容并解析为字典格式"""
        raw_email = self.fetch_email(email_id)
        if not raw_email:
            return None

        try:
            # 解析邮件
            msg = email.message_from_bytes(raw_email)

            # 提取基本信息
            email_data = {
                "id": email_id,
                "subject": self._decode_header(msg.get("Subject", "")),
                "from": self._decode_header(msg.get("From", "")),
                "to": self._decode_header(msg.get("To", "")),
                "date": msg.get("Date", ""),
                "body": "",
                "attachments": [],
            }

            # 提取邮件正文和附件
            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition", ""))

                    # 处理正文
                    if (
                        content_type == "text/plain"
                        and "attachment" not in content_disposition
                    ):
                        try:
                            body = part.get_payload(decode=True)
                            if body:
                                email_data["body"] = body.decode(
                                    "utf-8", errors="ignore"
                                )
                        except Exception as e:
                            self.logger.warning(f"解析邮件正文失败: {e}")

                    # 处理附件
                    elif "attachment" in content_disposition:
                        filename = part.get_filename()
                        if filename:
                            email_data["attachments"].append(
                                {
                                    "filename": self._decode_header(filename),
                                    "part": part,
                                }
                            )
            else:
                # 非多部分邮件
                try:
                    body = msg.get_payload(decode=True)
                    if body:
                        email_data["body"] = body.decode("utf-8", errors="ignore")
                except Exception as e:
                    self.logger.warning(f"解析邮件正文失败: {e}")

            return email_data

        except Exception as e:
            self.logger.error(f"解析邮件失败: {str(e)}")
            return None

    def _decode_header(self, header_value: str) -> str:
        """解码邮件头部信息"""
        if not header_value:
            return ""

        try:
            decoded_parts = email.header.decode_header(header_value)
            decoded_string = ""

            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_string += part.decode(encoding, errors="ignore")
                    else:
                        decoded_string += part.decode("utf-8", errors="ignore")
                else:
                    decoded_string += str(part)

            return decoded_string
        except Exception as e:
            self.logger.warning(f"解码头部信息失败: {e}")
            return str(header_value)
