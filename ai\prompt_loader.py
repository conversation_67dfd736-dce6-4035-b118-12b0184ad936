import os
import json

TEMPLATE_DIR = "prompt_templates"


class PromptLoader:
    def __init__(self, template_dir=TEMPLATE_DIR):
        self.template_dir = template_dir
        self.display_names = {}  # 初始化 display_names 属性

    def load(self, key: str) -> dict:
        path = os.path.join(self.template_dir, key)
        if not os.path.exists(path):
            path = os.path.join(self.template_dir, "default.json")
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)

    def get_display_name(self, key: str) -> str:
        entry = self.display_names.get(key) or self.display_names.get("default")
        return entry.get("display_name", key) if entry else key
