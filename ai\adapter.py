#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI适配器 - 支持多模型轮询和负载均衡

模块描述: 智能AI适配器，支持多个AI提供商的模型轮询，分离向量模型和大语言模型
作者: 开发团队
创建时间: 2025-01-27
版本: 2.0.0
依赖: requests, json, threading, time
"""

import json
import os
import requests
import time
import threading
import random
import logging
from typing import Dict, Any, List, Optional, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

CONFIG_PATH = os.path.join(os.path.dirname(__file__), "ai_config.json")


class ModelType(Enum):
    """模型类型枚举"""

    LLM = "llm"  # 大语言模型
    EMBEDDING = "embedding"  # 向量模型
    VISION = "vision"  # 视觉模型
    AUDIO = "audio"  # 音频模型


@dataclass
class ModelInfo:
    """模型信息"""

    name: str
    provider: str
    model_type: ModelType
    api_url: str
    api_key: str
    max_tokens: int
    temperature: float
    max_context_length: int
    timeout: int
    priority: int = 1  # 优先级，数字越小优先级越高
    is_available: bool = True
    last_used: float = 0.0
    error_count: int = 0
    max_errors: int = 3


class AIModelManager:
    """AI模型管理器 - 负责模型轮询和负载均衡"""

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or CONFIG_PATH
        self.models: Dict[ModelType, List[ModelInfo]] = {
            ModelType.LLM: [],
            ModelType.EMBEDDING: [],
            ModelType.VISION: [],
            ModelType.AUDIO: [],
        }
        self.current_model_index: Dict[ModelType, int] = {
            ModelType.LLM: 0,
            ModelType.EMBEDDING: 0,
            ModelType.VISION: 0,
            ModelType.AUDIO: 0,
        }
        self.lock = threading.Lock()
        self.load_config()

    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            disabled_providers = set(config.get("disabled_providers", []))

            # 解析配置并创建模型信息
            for provider_name, provider_config in config.items():
                if provider_name in ["disabled_providers"]:
                    continue

                if provider_name in disabled_providers:
                    logger.info(f"跳过已禁用的提供商: {provider_name}")
                    continue

                # 确定模型类型
                model_type = self._determine_model_type(provider_name, provider_config)

                # 获取模型列表
                models = provider_config.get("models", [provider_config.get("model")])

                for model_name in models:
                    if model_name:
                        model_info = ModelInfo(
                            name=model_name,
                            provider=provider_name,
                            model_type=model_type,
                            api_url=provider_config.get(
                                "api_url", provider_config.get("base_url", "")
                            ),
                            api_key=provider_config.get("api_key", ""),
                            max_tokens=provider_config.get("max_tokens", 8000),
                            temperature=provider_config.get("temperature", 0.7),
                            max_context_length=provider_config.get(
                                "max_context_length", 64000
                            ),
                            timeout=provider_config.get("timeout", 120),
                            priority=self._get_model_priority(
                                provider_name, model_name
                            ),
                        )
                        self.models[model_type].append(model_info)

            # 按优先级排序
            for model_type in self.models:
                self.models[model_type].sort(key=lambda x: (x.priority, x.name))

            logger.info(
                f"加载了 {sum(len(models) for models in self.models.values())} 个模型"
            )
            for model_type, models in self.models.items():
                logger.info(f"{model_type.value}: {len(models)} 个模型")

        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            raise

    def _determine_model_type(
        self, provider_name: str, provider_config: Dict
    ) -> ModelType:
        """确定模型类型"""
        # 根据提供商名称和配置确定模型类型
        if "embedding" in provider_name.lower():
            return ModelType.EMBEDDING
        elif "vision" in provider_name.lower():
            return ModelType.VISION
        elif "audio" in provider_name.lower():
            return ModelType.AUDIO
        else:
            return ModelType.LLM  # 默认为大语言模型

    def _get_model_priority(self, provider_name: str, model_name: str) -> int:
        """获取模型优先级"""
        # 定义优先级规则
        priority_rules = {
            # 高性能模型优先级高
            "qwen3-235b-a22b": 1,
            "qwen-max": 2,
            "deepseek-reasoner": 2,
            "qwen-plus": 3,
            "deepseek-chat": 3,
            "qwen-turbo": 4,
            "qwen-long": 5,
            # 本地模型优先级较低
            "gemma3:27b": 6,
            "llama2:13b": 7,
            "mistral:7b": 8,
        }

        return priority_rules.get(model_name, 5)  # 默认优先级为5

    def get_next_model(self, model_type: ModelType) -> Optional[ModelInfo]:
        """获取下一个可用模型（轮询）"""
        with self.lock:
            available_models = [m for m in self.models[model_type] if m.is_available]

            if not available_models:
                logger.warning(f"没有可用的{model_type.value}模型")
                return None

            # 轮询策略：选择下一个模型
            current_index = self.current_model_index[model_type]
            model = available_models[current_index % len(available_models)]

            # 更新索引
            self.current_model_index[model_type] = (current_index + 1) % len(
                available_models
            )

            # 更新使用时间
            model.last_used = time.time()

            logger.info(f"选择模型: {model.provider}/{model.name}")
            return model

    def get_best_model(self, model_type: ModelType) -> Optional[ModelInfo]:
        """获取最佳模型（基于优先级和可用性）"""
        available_models = [m for m in self.models[model_type] if m.is_available]

        if not available_models:
            return None

        # 选择优先级最高且错误最少的模型
        best_model = min(available_models, key=lambda x: (x.priority, x.error_count))
        best_model.last_used = time.time()

        logger.info(f"选择最佳模型: {best_model.provider}/{best_model.name}")
        return best_model

    def mark_model_error(self, model: ModelInfo, error: Exception):
        """标记模型错误"""
        with self.lock:
            model.error_count += 1
            logger.warning(
                f"模型 {model.provider}/{model.name} 错误计数: {model.error_count}"
            )

            if model.error_count >= model.max_errors:
                model.is_available = False
                logger.error(f"模型 {model.provider}/{model.name} 已禁用（错误过多）")

    def reset_model_errors(self, model: ModelInfo):
        """重置模型错误计数"""
        with self.lock:
            model.error_count = 0
            model.is_available = True
            logger.info(f"重置模型 {model.provider}/{model.name} 错误计数")

    def get_model_stats(self) -> Dict[str, Any]:
        """获取模型统计信息"""
        stats = {}
        for model_type, models in self.models.items():
            available_count = sum(1 for m in models if m.is_available)
            total_count = len(models)

            stats[model_type.value] = {
                "total": total_count,
                "available": available_count,
                "models": [
                    {
                        "name": f"{m.provider}/{m.name}",
                        "available": m.is_available,
                        "error_count": m.error_count,
                        "last_used": m.last_used,
                    }
                    for m in models
                ],
            }

        return stats


class AIAdapter:
    """AI适配器 - 支持多模型轮询和负载均衡的统一接口"""

    def __init__(
        self,
        config_path: Optional[str] = None,
        preferred_provider: Optional[str] = None,
    ):
        """
        初始化AI适配器

        Args:
            config_path: 配置文件路径
            preferred_provider: 首选提供商
        """
        self.model_manager = AIModelManager(config_path)
        self.preferred_provider = preferred_provider
        self.request_count = 0
        self.success_count = 0
        self.error_count = 0

    def call(
        self,
        prompt: str,
        model_type: ModelType = ModelType.LLM,
        strategy: str = "round_robin",
        stop: Optional[List[str]] = None,
        max_retries: int = 3,
    ) -> str:
        """
        调用AI模型

        Args:
            prompt: 输入提示
            model_type: 模型类型
            strategy: 选择策略 (round_robin, best, preferred)
            stop: 停止词
            max_retries: 最大重试次数

        Returns:
            str: AI响应结果
        """
        self.request_count += 1

        for attempt in range(max_retries):
            try:
                # 选择模型
                model = self._select_model(model_type, strategy)
                if not model:
                    raise Exception(f"没有可用的{model_type.value}模型")

                # 调用模型
                result = self._call_model(model, prompt, stop)

                # 成功调用
                self.success_count += 1
                self.model_manager.reset_model_errors(model)

                logger.info(f"AI调用成功: {model.provider}/{model.name}")
                return result

            except Exception as e:
                logger.warning(f"AI调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")

                if model:
                    self.model_manager.mark_model_error(model, e)

                if attempt == max_retries - 1:
                    # 最后一次尝试失败
                    self.error_count += 1
                    return self._get_fallback_response(e)

                # 等待后重试
                time.sleep(min(2**attempt, 10))  # 指数退避

        return self._get_fallback_response(Exception("所有重试都失败"))

    def call_embedding(
        self, text: str, model_name: Optional[str] = None, **kwargs
    ) -> List[float]:
        """
        调用向量模型

        Args:
            text: 输入文本
            model_name: 指定模型名称
            **kwargs: 其他参数

        Returns:
            List[float]: 向量表示
        """
        try:
            # 选择向量模型
            model = self._select_model(ModelType.EMBEDDING, "best")
            if not model:
                logger.warning("没有可用的向量模型，返回模拟向量")
                return [0.0] * 768

            # 如果指定了模型名称，尝试使用指定模型
            if model_name:
                specific_models = [
                    m
                    for m in self.model_manager.models[ModelType.EMBEDDING]
                    if m.name == model_name and m.is_available
                ]
                if specific_models:
                    model = specific_models[0]

            # 调用向量模型
            result = self._call_embedding_model(model, text)

            logger.info(f"向量模型调用成功: {model.provider}/{model.name}")
            return result

        except Exception as e:
            logger.error(f"向量模型调用失败: {e}")
            return [0.0] * 768  # 返回模拟向量

    def _call_embedding_model(self, model: ModelInfo, text: str) -> List[float]:
        """调用具体的向量模型"""
        # 构建向量模型请求
        headers = self._build_headers(model)

        # 向量模型的请求格式不同
        if "alibaba" in model.provider.lower():
            payload = {
                "model": model.name,
                "input": {"texts": [text]},
                "parameters": {"text_type": "document"},
            }
        else:  # OpenAI格式
            payload = {"model": model.name, "input": text, "encoding_format": "float"}

        # 发送请求
        response = requests.post(
            model.api_url, headers=headers, json=payload, timeout=model.timeout
        )
        response.raise_for_status()

        # 解析向量响应
        result = response.json()
        return self._parse_embedding_response(result, model.provider)

    def call_batch(
        self,
        prompts: List[str],
        model_type: ModelType = ModelType.LLM,
        max_workers: int = 3,
    ) -> List[str]:
        """
        批量调用AI模型

        Args:
            prompts: 提示列表
            model_type: 模型类型
            max_workers: 最大并发数

        Returns:
            List[str]: 响应结果列表
        """
        results = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_prompt = {
                executor.submit(self.call, prompt, model_type): prompt
                for prompt in prompts
            }

            # 收集结果
            for future in as_completed(future_to_prompt):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"批量调用失败: {e}")
                    results.append(self._get_fallback_response(e))

        return results

    def _select_model(
        self, model_type: ModelType, strategy: str
    ) -> Optional[ModelInfo]:
        """选择模型"""
        if strategy == "round_robin":
            return self.model_manager.get_next_model(model_type)
        elif strategy == "best":
            return self.model_manager.get_best_model(model_type)
        elif strategy == "preferred" and self.preferred_provider:
            # 尝试使用首选提供商
            preferred_models = [
                m
                for m in self.model_manager.models[model_type]
                if m.provider == self.preferred_provider and m.is_available
            ]
            if preferred_models:
                return preferred_models[0]
            else:
                # 回退到轮询策略
                return self.model_manager.get_next_model(model_type)
        else:
            return self.model_manager.get_next_model(model_type)

    def _call_model(
        self, model: ModelInfo, prompt: str, stop: Optional[List[str]] = None
    ) -> str:
        """调用具体模型"""
        # 构建请求
        headers = self._build_headers(model)
        payload = self._build_payload(model, prompt, stop)

        # 发送请求
        response = requests.post(
            model.api_url, headers=headers, json=payload, timeout=model.timeout
        )
        response.raise_for_status()

        # 解析响应
        result = response.json()
        return self._parse_response(result)

    def _build_headers(self, model: ModelInfo) -> Dict[str, str]:
        """构建请求头"""
        headers = {"Content-Type": "application/json"}

        if model.api_key:
            headers["Authorization"] = f"Bearer {model.api_key}"

        return headers

    def _build_payload(
        self, model: ModelInfo, prompt: str, stop: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """构建请求载荷"""
        payload = {
            "model": model.name,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": model.temperature,
            "max_tokens": model.max_tokens,
        }

        if stop:
            payload["stop"] = stop

        return payload

    def _parse_response(self, response: Dict[str, Any]) -> str:
        """解析LLM响应"""
        try:
            return response["choices"][0]["message"]["content"]
        except (KeyError, IndexError) as e:
            raise Exception(f"响应格式错误: {e}")

    def _parse_embedding_response(
        self, response: Dict[str, Any], provider: str
    ) -> List[float]:
        """解析向量模型响应"""
        try:
            if "alibaba" in provider.lower():
                # 阿里云向量模型响应格式
                return response["output"]["embeddings"][0]["embedding"]
            else:
                # OpenAI向量模型响应格式
                return response["data"][0]["embedding"]
        except (KeyError, IndexError) as e:
            raise Exception(f"向量响应格式错误: {e}")

    def _get_fallback_response(self, error: Exception) -> str:
        """获取回退响应"""
        return json.dumps(
            {
                "email_type": "other",
                "confidence": 0.5,
                "reasoning": f"AI调用出错: {error}",
                "error": True,
            },
            ensure_ascii=False,
        )

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        model_stats = self.model_manager.get_model_stats()

        return {
            "adapter_stats": {
                "total_requests": self.request_count,
                "successful_requests": self.success_count,
                "failed_requests": self.error_count,
                "success_rate": self.success_count / max(self.request_count, 1) * 100,
            },
            "model_stats": model_stats,
        }


if __name__ == "__main__":
    # 简单测试
    try:
        adapter = AIAdapter()
        prompt = "请用一句话介绍你自己。"

        print("=== AI适配器测试 ===")
        print(f"提示: {prompt}")

        # 测试轮询策略
        print("\n--- 轮询策略测试 ---")
        result1 = adapter.call(prompt, strategy="round_robin")
        print(f"结果1: {result1}")

        result2 = adapter.call(prompt, strategy="round_robin")
        print(f"结果2: {result2}")

        # 测试最佳模型策略
        print("\n--- 最佳模型策略测试 ---")
        result3 = adapter.call(prompt, strategy="best")
        print(f"结果3: {result3}")

        # 显示统计信息
        print("\n--- 统计信息 ---")
        stats = adapter.get_stats()
        print(json.dumps(stats, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback

        traceback.print_exc()
