#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
核心模块包
提供系统基础组件和通用功能

模块描述: 核心基础设施模块，提供所有其他模块的基础抽象类和接口
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: typing, abc, logging
"""

__version__ = "1.0.0"
__author__ = "开发团队"
__status__ = "Development"

# 直接导入所有组件，避免动态导入导致的 Pylance 解析问题
try:
    # 基础组件
    from .base import BaseComponent, BaseService, BaseRepository
    from .interfaces import IAnalyzer, IVisualizer, IDataProcessor
    from .decorators import performance_monitor, cache_result, error_handler
    from .exceptions import CoreException, ValidationError, ProcessingError
    from .types import AnalysisResult, ComponentConfig, ServiceResponse

    # 设置 __all__
    __all__ = [
        "BaseComponent",
        "BaseService",
        "BaseRepository",
        "IAnalyzer",
        "IVisualizer",
        "IDataProcessor",
        "performance_monitor",
        "cache_result",
        "error_handler",
        "CoreException",
        "ValidationError",
        "ProcessingError",
        "AnalysisResult",
        "ComponentConfig",
        "ServiceResponse",
    ]

except ImportError as e:
    # 开发阶段可能出现的导入错误
    import logging

    logging.warning(f"核心模块导入警告: {e}")
    __all__ = []
