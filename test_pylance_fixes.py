#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试 Pylance 修复效果
验证主要的导入和类型问题是否已解决
"""

import sys
import os
import json
from datetime import datetime, timedelta

print("🔧 Pylance 修复验证测试")
print("=" * 50)

# 测试1: 基础导入
print("\n1. 测试基础导入...")
try:
    import json
    from datetime import datetime, timedelta

    print("✅ 基础导入成功")
except Exception as e:
    print(f"❌ 基础导入失败: {e}")

# 测试2: 配置导入
print("\n2. 测试配置导入...")
try:
    # 添加当前目录到路径
    import sys

    if "." not in sys.path:
        sys.path.insert(0, ".")

    # 导入根目录的config.py
    import config

    DB_CONFIG = config.DB_CONFIG
    IMAP_CONFIG = config.IMAP_CONFIG
    LOG_CONFIG = config.LOG_CONFIG

    print("✅ 配置导入成功")
    print(f"   数据库主机: {DB_CONFIG.get('host', 'N/A')}")
    print(f"   邮件服务器: {IMAP_CONFIG.get('server', 'N/A')}")
    print(f"   日志级别: {LOG_CONFIG.get('level', 'N/A')}")
except Exception as e:
    print(f"❌ 配置导入失败: {e}")

# 测试3: 核心模块导入
print("\n3. 测试核心模块导入...")
try:
    from core import BaseComponent, ComponentConfig

    print("✅ 核心模块导入成功")

    # 测试创建配置
    config = ComponentConfig(name="test", version="1.0.0")
    print(f"   配置创建成功: {config.name} v{config.version}")
except Exception as e:
    print(f"❌ 核心模块导入失败: {e}")

# 测试4: 邮件模块导入
print("\n4. 测试邮件模块导入...")
try:
    from email_module.email_connection import EmailConnection

    print("✅ 邮件连接模块导入成功")

    # 检查新添加的方法
    methods = ["select_mailbox", "get_email", "search_emails"]
    for method in methods:
        if hasattr(EmailConnection, method):
            print(f"   ✅ 方法存在: {method}")
        else:
            print(f"   ❌ 方法缺失: {method}")

except Exception as e:
    print(f"❌ 邮件模块导入失败: {e}")

# 测试5: 时间计算
print("\n5. 测试时间计算...")
try:
    now = datetime.now()
    yesterday = now - timedelta(days=1)
    tomorrow = now + timedelta(days=1)

    print(f"✅ 时间计算成功")
    print(f"   现在: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   昨天: {yesterday.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   明天: {tomorrow.strftime('%Y-%m-%d %H:%M:%S')}")
except Exception as e:
    print(f"❌ 时间计算失败: {e}")

# 测试6: JSON序列化
print("\n6. 测试JSON序列化...")
try:
    test_data = {
        "test_time": datetime.now().isoformat(),
        "status": "success",
        "data": {"message": "Pylance修复测试", "version": "1.0.0"},
    }

    json_str = json.dumps(test_data, ensure_ascii=False, indent=2)
    print("✅ JSON序列化成功")

    # 保存测试结果
    with open("pylance_fix_test_result.json", "w", encoding="utf-8") as f:
        f.write(json_str)
    print("✅ 测试结果已保存")

except Exception as e:
    print(f"❌ JSON序列化失败: {e}")

# 测试7: 领域模块导入
print("\n7. 测试领域模块导入...")
try:
    from domain import Employee, WeeklyReport, AnalysisResult

    print("✅ 领域模块导入成功")
except Exception as e:
    print(f"❌ 领域模块导入失败: {e}")

# 测试8: 服务模块导入
print("\n8. 测试服务模块导入...")
try:
    from services.data import DataService, CacheService

    print("✅ 数据服务模块导入成功")
except Exception as e:
    print(f"❌ 数据服务模块导入失败: {e}")

print("\n" + "=" * 50)
print("🎯 Pylance 修复验证完成")
print("如果大部分测试显示 ✅，说明主要问题已修复")
print("剩余的 ❌ 可能需要进一步处理或安装依赖包")
