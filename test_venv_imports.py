#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试虚拟环境包导入
"""

import sys

def test_imports():
    """测试关键包导入"""
    print("🔍 虚拟环境包导入测试")
    print("=" * 40)
    print(f"🐍 Python 解释器: {sys.executable}")
    print()
    
    packages = [
        'psycopg2',
        'streamlit', 
        'plotly',
        'scipy',
        'fastapi',
        'pandas',
        'numpy'
    ]
    
    success_count = 0
    for pkg in packages:
        try:
            __import__(pkg)
            print(f"✅ {pkg} - 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"❌ {pkg} - 导入失败: {e}")
    
    print()
    print(f"📊 导入成功率: {success_count}/{len(packages)} ({success_count/len(packages)*100:.1f}%)")
    
    if success_count == len(packages):
        print("🎉 所有关键包都可以正常导入！")
        return True
    else:
        print("⚠️ 部分包导入失败，请检查虚拟环境配置")
        return False

if __name__ == "__main__":
    test_imports()
