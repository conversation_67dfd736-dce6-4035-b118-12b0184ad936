#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
聚类分析器

模块描述: 基于聚类算法的数据分析器，支持多种聚类方法
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: core.interfaces, domain.entities, sklearn, numpy
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import numpy as np
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.decomposition import PCA
import pandas as pd

from core.interfaces import IAnalyzer
from domain.entities import WeeklyReport, AnalysisResult, TaskComplexity, TaskCategory
from domain.value_objects import ProcessingContext


class ClusteringAnalyzer(IAnalyzer):
    """
    聚类分析器

    功能：
    - 工作项聚类分析
    - 员工工作模式聚类
    - 任务复杂度聚类
    - 时间分布聚类
    - 部门工作特征聚类
    """

    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化聚类分析器

        Args:
            config: 配置参数
        """
        self.name = "clustering_analyzer"
        self.version = "1.0.0"
        self.config = config or {}

        # 聚类算法配置
        self.clustering_methods = {
            "kmeans": KMeans,
            "dbscan": DBSCAN,
            "hierarchical": AgglomerativeClustering,
        }

        # 默认参数
        self.default_params = {
            "kmeans": {"n_clusters": 3, "random_state": 42},
            "dbscan": {"eps": 0.5, "min_samples": 2},
            "hierarchical": {"n_clusters": 3},
        }

        self.scaler = StandardScaler()

    def get_name(self) -> str:
        """获取分析器名称"""
        return self.name

    def get_version(self) -> str:
        """获取版本"""
        return self.version

    def get_description(self) -> str:
        """获取描述"""
        return "聚类分析器，支持工作项聚类、员工模式聚类、任务复杂度聚类等多种聚类分析"

    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ["weekly_report", "work_items", "employee_data", "batch_reports"]

    def validate_input(self, data: Any) -> bool:
        """
        验证输入数据

        Args:
            data: 输入数据

        Returns:
            bool: 数据是否有效
        """
        if not isinstance(data, WeeklyReport):
            return False

        # 检查必要字段
        if not hasattr(data, "report_id") or not data.report_id:
            return False

        if not hasattr(data, "work_items") or not isinstance(data.work_items, list):
            return False

        # 聚类分析至少需要2个数据点
        if len(data.work_items) < 2:
            return False

        # 检查工作项数据
        for item in data.work_items:
            if not hasattr(item, "duration_hours") or not isinstance(
                item.duration_hours, (int, float)
            ):
                return False
            if not hasattr(item, "complexity") or item.complexity is None:
                return False
            if not hasattr(item, "category") or item.category is None:
                return False
            if not hasattr(item, "title") or not item.title:
                return False

        return True

    def initialize(self) -> bool:
        """初始化分析器"""
        try:
            # 初始化标准化器
            self.scaler = StandardScaler()
            return True
        except Exception as e:
            print(f"聚类分析器初始化失败: {e}")
            return False

    def analyze(
        self, data: WeeklyReport, context: ProcessingContext = None
    ) -> AnalysisResult:
        """
        执行聚类分析

        Args:
            data: 周报数据
            context: 处理上下文

        Returns:
            AnalysisResult: 聚类分析结果
        """
        start_time = datetime.now()

        try:
            results = {}

            # 1. 工作项聚类分析
            if data.work_items:
                work_item_clusters = self._analyze_work_item_clusters(data.work_items)
                results["work_item_clusters"] = work_item_clusters

            # 2. 任务复杂度聚类
            complexity_clusters = self._analyze_complexity_clusters(data.work_items)
            results["complexity_clusters"] = complexity_clusters

            # 3. 时间分布聚类
            time_clusters = self._analyze_time_clusters(data.work_items)
            results["time_clusters"] = time_clusters

            # 4. 工作模式识别
            work_patterns = self._identify_work_patterns(data)
            results["work_patterns"] = work_patterns

            # 5. 聚类质量评估
            cluster_quality = self._evaluate_cluster_quality(results)
            results["cluster_quality"] = cluster_quality

            # 6. 聚类洞察和建议
            insights = self._generate_clustering_insights(results, data)
            results["insights"] = insights

            # 计算置信度
            confidence_score = self._calculate_confidence(results)

            processing_time = (datetime.now() - start_time).total_seconds()

            return AnalysisResult(
                result_id=f"clustering_{data.report_id}_{int(datetime.now().timestamp())}",
                report_id=data.report_id,
                analysis_type=self.name,
                result_data=results,
                confidence_score=confidence_score,
                model_version=self.version,
                processing_time=processing_time,
            )

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            return AnalysisResult(
                result_id=f"clustering_error_{data.report_id}_{int(datetime.now().timestamp())}",
                report_id=data.report_id,
                analysis_type=self.name,
                result_data={"error": str(e)},
                confidence_score=0.0,
                model_version=self.version,
                processing_time=processing_time,
            )

    def _analyze_work_item_clusters(self, work_items: List) -> Dict[str, Any]:
        """分析工作项聚类"""
        if len(work_items) < 2:
            return {"error": "工作项数量不足，无法进行聚类分析"}

        # 提取工作项特征
        features = []
        item_info = []

        for item in work_items:
            feature_vector = [
                item.duration_hours,
                self._complexity_to_numeric(item.complexity),
                self._category_to_numeric(item.category),
                len(item.description) if item.description else 0,
            ]
            features.append(feature_vector)
            item_info.append(
                {
                    "title": item.title,
                    "duration": item.duration_hours,
                    "complexity": item.complexity.value,
                    "category": item.category.value,
                }
            )

        # 标准化特征
        features_scaled = self.scaler.fit_transform(features)

        # 执行聚类
        clusters_results = {}

        # K-means聚类
        n_clusters = min(3, len(work_items))
        if n_clusters >= 2:
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            kmeans_labels = kmeans.fit_predict(features_scaled)

            clusters_results["kmeans"] = {
                "labels": kmeans_labels.tolist(),
                "centers": kmeans.cluster_centers_.tolist(),
                "n_clusters": n_clusters,
                "silhouette_score": (
                    silhouette_score(features_scaled, kmeans_labels)
                    if len(set(kmeans_labels)) > 1
                    else 0
                ),
            }

            # 分析每个聚类的特征
            cluster_analysis = self._analyze_cluster_characteristics(
                features, kmeans_labels, item_info
            )
            clusters_results["kmeans"]["cluster_analysis"] = cluster_analysis

        return clusters_results

    def _analyze_complexity_clusters(self, work_items: List) -> Dict[str, Any]:
        """分析任务复杂度聚类"""
        if not work_items:
            return {"error": "无工作项数据"}

        # 按复杂度分组
        complexity_groups = {"low": [], "medium": [], "high": []}

        for item in work_items:
            complexity_key = item.complexity.value.lower()
            complexity_groups[complexity_key].append(
                {
                    "title": item.title,
                    "duration": item.duration_hours,
                    "category": item.category.value,
                    "description_length": (
                        len(item.description) if item.description else 0
                    ),
                }
            )

        # 分析每个复杂度组的特征
        complexity_analysis = {}
        for complexity, items in complexity_groups.items():
            if items:
                durations = [item["duration"] for item in items]
                complexity_analysis[complexity] = {
                    "count": len(items),
                    "avg_duration": np.mean(durations),
                    "total_duration": sum(durations),
                    "duration_std": np.std(durations),
                    "categories": list(set(item["category"] for item in items)),
                    "avg_description_length": np.mean(
                        [item["description_length"] for item in items]
                    ),
                }

        return {
            "complexity_distribution": complexity_analysis,
            "insights": self._generate_complexity_insights(complexity_analysis),
        }

    def _analyze_time_clusters(self, work_items: List) -> Dict[str, Any]:
        """分析时间分布聚类"""
        if not work_items:
            return {"error": "无工作项数据"}

        durations = [item.duration_hours for item in work_items]

        if len(durations) < 2:
            return {"error": "数据点不足，无法进行时间聚类"}

        # 基于工时进行聚类
        duration_array = np.array(durations).reshape(-1, 1)
        duration_scaled = self.scaler.fit_transform(duration_array)

        # K-means聚类
        n_clusters = min(3, len(durations))
        if n_clusters >= 2:
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            labels = kmeans.fit_predict(duration_scaled)

            # 分析时间聚类
            time_clusters = {}
            for i in range(n_clusters):
                cluster_items = [
                    work_items[j] for j, label in enumerate(labels) if label == i
                ]
                cluster_durations = [item.duration_hours for item in cluster_items]

                time_clusters[f"cluster_{i}"] = {
                    "count": len(cluster_items),
                    "avg_duration": np.mean(cluster_durations),
                    "min_duration": min(cluster_durations),
                    "max_duration": max(cluster_durations),
                    "duration_range": max(cluster_durations) - min(cluster_durations),
                    "items": [
                        {"title": item.title, "duration": item.duration_hours}
                        for item in cluster_items[:5]
                    ],  # 只显示前5个
                }

            return {
                "time_clusters": time_clusters,
                "silhouette_score": (
                    silhouette_score(duration_scaled, labels)
                    if len(set(labels)) > 1
                    else 0
                ),
                "insights": self._generate_time_insights(time_clusters),
            }

        return {"error": "无法形成有效的时间聚类"}

    def _identify_work_patterns(self, report: WeeklyReport) -> Dict[str, Any]:
        """识别工作模式"""
        if not report.work_items:
            return {"error": "无工作项数据"}

        # 分析工作模式特征
        total_hours = sum(item.duration_hours for item in report.work_items)

        # 类别分布
        category_hours = {}
        for item in report.work_items:
            category = item.category.value
            category_hours[category] = (
                category_hours.get(category, 0) + item.duration_hours
            )

        # 复杂度分布
        complexity_hours = {}
        for item in report.work_items:
            complexity = item.complexity.value
            complexity_hours[complexity] = (
                complexity_hours.get(complexity, 0) + item.duration_hours
            )

        # 识别工作模式
        patterns = []

        # 专注型模式：主要类别占比超过60%
        if category_hours:
            max_category = max(category_hours.items(), key=lambda x: x[1])
            if max_category[1] / total_hours > 0.6:
                patterns.append(
                    {
                        "type": "focused",
                        "description": f"专注于{max_category[0]}工作",
                        "percentage": max_category[1] / total_hours * 100,
                    }
                )

        # 多样化模式：类别数量较多且分布均匀
        if len(category_hours) >= 4:
            patterns.append(
                {
                    "type": "diversified",
                    "description": "工作内容多样化",
                    "category_count": len(category_hours),
                }
            )

        # 高强度模式：总工时超过45小时
        if total_hours > 45:
            patterns.append(
                {
                    "type": "high_intensity",
                    "description": "高强度工作模式",
                    "total_hours": total_hours,
                }
            )

        # 复杂任务模式：高复杂度任务占比超过40%
        if "HIGH" in complexity_hours:
            high_complexity_ratio = complexity_hours["HIGH"] / total_hours
            if high_complexity_ratio > 0.4:
                patterns.append(
                    {
                        "type": "complex_tasks",
                        "description": "主要处理复杂任务",
                        "percentage": high_complexity_ratio * 100,
                    }
                )

        return {
            "identified_patterns": patterns,
            "category_distribution": category_hours,
            "complexity_distribution": complexity_hours,
            "total_hours": total_hours,
            "work_diversity_score": len(category_hours)
            / max(1, len(report.work_items))
            * 100,
        }

    def _evaluate_cluster_quality(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """评估聚类质量"""
        quality_metrics = {}

        # 评估工作项聚类质量
        if (
            "work_item_clusters" in results
            and "kmeans" in results["work_item_clusters"]
        ):
            kmeans_result = results["work_item_clusters"]["kmeans"]
            quality_metrics["work_item_clustering"] = {
                "silhouette_score": kmeans_result.get("silhouette_score", 0),
                "n_clusters": kmeans_result.get("n_clusters", 0),
                "quality_level": self._assess_quality_level(
                    kmeans_result.get("silhouette_score", 0)
                ),
            }

        # 评估时间聚类质量
        if "time_clusters" in results:
            time_silhouette = results["time_clusters"].get("silhouette_score", 0)
            quality_metrics["time_clustering"] = {
                "silhouette_score": time_silhouette,
                "quality_level": self._assess_quality_level(time_silhouette),
            }

        return quality_metrics

    def _generate_clustering_insights(
        self, results: Dict[str, Any], report: WeeklyReport
    ) -> List[str]:
        """生成聚类洞察"""
        insights = []

        # 工作项聚类洞察
        if (
            "work_item_clusters" in results
            and "kmeans" in results["work_item_clusters"]
        ):
            cluster_analysis = results["work_item_clusters"]["kmeans"].get(
                "cluster_analysis", {}
            )
            for cluster_id, analysis in cluster_analysis.items():
                if analysis["avg_duration"] > 8:
                    insights.append(
                        f"聚类{cluster_id}包含长时间任务，平均{analysis['avg_duration']:.1f}小时"
                    )
                if analysis["dominant_complexity"] == "HIGH":
                    insights.append(f"聚类{cluster_id}主要为高复杂度任务")

        # 工作模式洞察
        if "work_patterns" in results:
            patterns = results["work_patterns"].get("identified_patterns", [])
            for pattern in patterns:
                if pattern["type"] == "focused":
                    insights.append(f"工作模式专注，{pattern['description']}")
                elif pattern["type"] == "high_intensity":
                    insights.append(
                        f"工作强度较高，总工时{pattern['total_hours']:.1f}小时"
                    )

        # 复杂度分布洞察
        if "complexity_clusters" in results:
            complexity_insights = results["complexity_clusters"].get("insights", [])
            insights.extend(complexity_insights)

        return insights

    def _analyze_cluster_characteristics(
        self, features: List, labels: List, item_info: List
    ) -> Dict[str, Any]:
        """分析聚类特征"""
        cluster_analysis = {}

        for cluster_id in set(labels):
            cluster_indices = [
                i for i, label in enumerate(labels) if label == cluster_id
            ]
            cluster_features = [features[i] for i in cluster_indices]
            cluster_items = [item_info[i] for i in cluster_indices]

            # 计算聚类统计信息
            durations = [item["duration"] for item in cluster_items]
            complexities = [item["complexity"] for item in cluster_items]
            categories = [item["category"] for item in cluster_items]

            cluster_analysis[f"cluster_{cluster_id}"] = {
                "size": len(cluster_items),
                "avg_duration": np.mean(durations),
                "duration_std": np.std(durations),
                "dominant_complexity": max(set(complexities), key=complexities.count),
                "dominant_category": max(set(categories), key=categories.count),
                "complexity_distribution": {
                    comp: complexities.count(comp) for comp in set(complexities)
                },
                "category_distribution": {
                    cat: categories.count(cat) for cat in set(categories)
                },
            }

        return cluster_analysis

    def _generate_complexity_insights(
        self, complexity_analysis: Dict[str, Any]
    ) -> List[str]:
        """生成复杂度洞察"""
        insights = []

        total_items = sum(
            analysis["count"] for analysis in complexity_analysis.values()
        )

        for complexity, analysis in complexity_analysis.items():
            percentage = analysis["count"] / total_items * 100
            if percentage > 50:
                insights.append(
                    f"{complexity.upper()}复杂度任务占主导地位({percentage:.1f}%)"
                )

            if analysis["avg_duration"] > 6:
                insights.append(
                    f"{complexity.upper()}复杂度任务平均耗时较长({analysis['avg_duration']:.1f}小时)"
                )

        return insights

    def _generate_time_insights(self, time_clusters: Dict[str, Any]) -> List[str]:
        """生成时间洞察"""
        insights = []

        for cluster_name, cluster_info in time_clusters.items():
            avg_duration = cluster_info["avg_duration"]
            if avg_duration > 8:
                insights.append(f"存在长时间任务聚类，平均{avg_duration:.1f}小时")
            elif avg_duration < 2:
                insights.append(f"存在短时间任务聚类，平均{avg_duration:.1f}小时")

        return insights

    def _complexity_to_numeric(self, complexity: TaskComplexity) -> float:
        """将复杂度转换为数值"""
        mapping = {
            TaskComplexity.LOW: 1.0,
            TaskComplexity.MEDIUM: 2.0,
            TaskComplexity.HIGH: 3.0,
        }
        return mapping.get(complexity, 1.0)

    def _category_to_numeric(self, category: TaskCategory) -> float:
        """将类别转换为数值（简化实现）"""
        categories = list(TaskCategory)
        try:
            return float(categories.index(category))
        except ValueError:
            return 0.0

    def _assess_quality_level(self, silhouette_score: float) -> str:
        """评估聚类质量等级"""
        if silhouette_score > 0.7:
            return "excellent"
        elif silhouette_score > 0.5:
            return "good"
        elif silhouette_score > 0.25:
            return "fair"
        else:
            return "poor"

    def _calculate_confidence(self, results: Dict[str, Any]) -> float:
        """计算整体置信度"""
        confidence_scores = []

        # 基于聚类质量评估置信度
        if "cluster_quality" in results:
            for clustering_type, quality in results["cluster_quality"].items():
                silhouette = quality.get("silhouette_score", 0)
                confidence_scores.append(max(0, silhouette))

        # 基于识别的模式数量
        if "work_patterns" in results:
            pattern_count = len(results["work_patterns"].get("identified_patterns", []))
            pattern_confidence = min(1.0, pattern_count / 3)  # 最多3个模式为满分
            confidence_scores.append(pattern_confidence)

        # 默认置信度
        if not confidence_scores:
            confidence_scores.append(0.6)

        return sum(confidence_scores) / len(confidence_scores)
