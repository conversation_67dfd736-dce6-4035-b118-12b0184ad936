#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
工作流引擎

模块描述: 管理和执行工作流程，协调各个服务组件
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
"""

import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from enum import Enum
from dataclasses import dataclass
from core import BaseService, ServiceResponse, ComponentConfig


class WorkflowStatus(Enum):
    """工作流状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class WorkflowStep:
    """工作流步骤"""

    name: str
    handler: Callable
    dependencies: Optional[List[str]] = None
    timeout: int = 300
    retry_count: int = 3

    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


@dataclass
class WorkflowResult:
    """工作流执行结果"""

    workflow_id: str
    status: WorkflowStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    results: Optional[Dict[str, Any]] = None
    errors: Optional[List[str]] = None

    def __post_init__(self):
        if self.results is None:
            self.results = {}
        if self.errors is None:
            self.errors = []


class WorkflowEngine(BaseService):
    """
    工作流引擎

    负责管理和执行复杂的业务流程，协调各个服务组件
    """

    def __init__(self, config: Optional[ComponentConfig] = None):
        if config is None:
            config = ComponentConfig(name="workflow_engine", version="1.0.0")
        super().__init__(config)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.workflows: Dict[str, Dict[str, WorkflowStep]] = {}
        self.running_workflows: Dict[str, WorkflowResult] = {}

    def register_workflow(
        self, workflow_id: str, steps: List[WorkflowStep]
    ) -> ServiceResponse:
        """
        注册工作流

        Args:
            workflow_id: 工作流ID
            steps: 工作流步骤列表

        Returns:
            ServiceResponse: 注册结果
        """
        try:
            # 验证步骤依赖关系
            step_names = {step.name for step in steps}
            for step in steps:
                for dep in step.dependencies or []:
                    if dep not in step_names:
                        return ServiceResponse(
                            success=False,
                            error_message=f"步骤 {step.name} 的依赖 {dep} 不存在",
                        )

            # 注册工作流
            self.workflows[workflow_id] = {step.name: step for step in steps}

            self.logger.info(f"工作流 {workflow_id} 注册成功，包含 {len(steps)} 个步骤")
            return ServiceResponse(
                success=True,
                data={"workflow_id": workflow_id, "step_count": len(steps)},
            )

        except Exception as e:
            self.logger.error(f"注册工作流失败: {e}")
            return ServiceResponse(
                success=False, error_message=f"注册工作流失败: {str(e)}"
            )

    def execute_workflow(
        self, workflow_id: str, context: Optional[Dict[str, Any]] = None
    ) -> ServiceResponse:
        """
        执行工作流

        Args:
            workflow_id: 工作流ID
            context: 执行上下文

        Returns:
            ServiceResponse: 执行结果
        """
        if context is None:
            context = {}

        try:
            if workflow_id not in self.workflows:
                return ServiceResponse(
                    success=False, error_message=f"工作流 {workflow_id} 不存在"
                )

            # 创建工作流结果
            result = WorkflowResult(
                workflow_id=workflow_id,
                status=WorkflowStatus.RUNNING,
                start_time=datetime.now(),
            )

            self.running_workflows[workflow_id] = result

            # 执行工作流步骤
            steps = self.workflows[workflow_id]
            executed_steps = set()

            while len(executed_steps) < len(steps):
                # 找到可以执行的步骤
                ready_steps = []
                for step_name, step in steps.items():
                    if step_name not in executed_steps and all(
                        dep in executed_steps for dep in (step.dependencies or [])
                    ):
                        ready_steps.append(step)

                if not ready_steps:
                    # 检查是否有循环依赖
                    remaining_steps = set(steps.keys()) - executed_steps
                    result.status = WorkflowStatus.FAILED
                    if result.errors is not None:
                        result.errors.append(
                            f"检测到循环依赖或无法满足的依赖: {remaining_steps}"
                        )
                    break

                # 执行准备好的步骤
                for step in ready_steps:
                    try:
                        self.logger.info(f"执行步骤: {step.name}")
                        step_result = step.handler(context)
                        if result.results is not None:
                            result.results[step.name] = step_result
                        executed_steps.add(step.name)
                        self.logger.info(f"步骤 {step.name} 执行成功")

                    except Exception as e:
                        self.logger.error(f"步骤 {step.name} 执行失败: {e}")
                        result.status = WorkflowStatus.FAILED
                        if result.errors is not None:
                            result.errors.append(f"步骤 {step.name} 执行失败: {str(e)}")
                        break

                if result.status == WorkflowStatus.FAILED:
                    break

            # 完成工作流
            if result.status != WorkflowStatus.FAILED:
                result.status = WorkflowStatus.COMPLETED

            result.end_time = datetime.now()

            self.logger.info(
                f"工作流 {workflow_id} 执行完成，状态: {result.status.value}"
            )

            return ServiceResponse(
                success=result.status == WorkflowStatus.COMPLETED,
                data=result.__dict__,
            )

        except Exception as e:
            self.logger.error(f"执行工作流失败: {e}")
            return ServiceResponse(
                success=False, error_message=f"执行工作流失败: {str(e)}"
            )

    def get_workflow_status(self, workflow_id: str) -> ServiceResponse:
        """
        获取工作流状态

        Args:
            workflow_id: 工作流ID

        Returns:
            ServiceResponse: 状态信息
        """
        try:
            if workflow_id in self.running_workflows:
                result = self.running_workflows[workflow_id]
                return ServiceResponse(success=True, data=result.__dict__)
            else:
                return ServiceResponse(
                    success=False, error_message=f"工作流 {workflow_id} 不存在或未运行"
                )

        except Exception as e:
            self.logger.error(f"获取工作流状态失败: {e}")
            return ServiceResponse(
                success=False, error_message=f"获取工作流状态失败: {str(e)}"
            )

    def list_workflows(self) -> ServiceResponse:
        """
        列出所有注册的工作流

        Returns:
            ServiceResponse: 工作流列表
        """
        try:
            workflows = []
            for workflow_id, steps in self.workflows.items():
                workflows.append(
                    {
                        "workflow_id": workflow_id,
                        "step_count": len(steps),
                        "steps": list(steps.keys()),
                    }
                )

            return ServiceResponse(success=True, data={"workflows": workflows})

        except Exception as e:
            self.logger.error(f"列出工作流失败: {e}")
            return ServiceResponse(
                success=False, error_message=f"列出工作流失败: {str(e)}"
            )
