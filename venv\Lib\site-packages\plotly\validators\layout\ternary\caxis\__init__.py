import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._uirevision.UirevisionValidator",
        "._title.TitleValidator",
        "._tickwidth.TickwidthValidator",
        "._tickvalssrc.TickvalssrcValidator",
        "._tickvals.TickvalsValidator",
        "._ticktextsrc.TicktextsrcValidator",
        "._ticktext.TicktextValidator",
        "._ticksuffix.TicksuffixValidator",
        "._ticks.TicksValidator",
        "._tickprefix.TickprefixValidator",
        "._tickmode.TickmodeValidator",
        "._ticklen.TicklenValidator",
        "._ticklabelstep.TicklabelstepValidator",
        "._tickformatstopdefaults.TickformatstopdefaultsValidator",
        "._tickformatstops.TickformatstopsValidator",
        "._tickformat.TickformatValidator",
        "._tickfont.TickfontValidator",
        "._tickcolor.TickcolorValidator",
        "._tickangle.TickangleValidator",
        "._tick0.Tick0Validator",
        "._showticksuffix.ShowticksuffixValidator",
        "._showtickprefix.ShowtickprefixValidator",
        "._showticklabels.ShowticklabelsValidator",
        "._showline.ShowlineValidator",
        "._showgrid.ShowgridValidator",
        "._showexponent.ShowexponentValidator",
        "._separatethousands.SeparatethousandsValidator",
        "._nticks.NticksValidator",
        "._minexponent.MinexponentValidator",
        "._min.MinValidator",
        "._linewidth.LinewidthValidator",
        "._linecolor.LinecolorValidator",
        "._layer.LayerValidator",
        "._labelalias.LabelaliasValidator",
        "._hoverformat.HoverformatValidator",
        "._gridwidth.GridwidthValidator",
        "._griddash.GriddashValidator",
        "._gridcolor.GridcolorValidator",
        "._exponentformat.ExponentformatValidator",
        "._dtick.DtickValidator",
        "._color.ColorValidator",
    ],
)
