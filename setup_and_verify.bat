@echo off
echo ========================================
echo ZKTeco 邮件分析系统 - 环境设置和验证
echo ========================================
echo.

cd /d F:\jj\ali\dataann_email\zkteco_js

echo 1. 检查虚拟环境...
if exist "venv\Scripts\activate.bat" (
    echo ✅ 虚拟环境已存在
) else (
    echo ❌ 虚拟环境不存在，正在创建...
    cd /d F:\jj\ali\dataann_email
    python -m venv zkteco_js\venv
    if errorlevel 1 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
    cd /d F:\jj\ali\dataann_email\zkteco_js
)

echo.
echo 2. 激活虚拟环境并安装依赖...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ 虚拟环境激活失败
    pause
    exit /b 1
)

echo ✅ 虚拟环境已激活
echo 正在安装/更新依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装完成

echo.
echo 3. 运行 Pylance 修复验证测试...
python test_pylance_fixes.py
if errorlevel 1 (
    echo ❌ 验证测试失败
    pause
    exit /b 1
)

echo.
echo 4. 检查关键模块导入...
python -c "
try:
    from core import BaseComponent, ServiceResponse, ComponentConfig
    from services.email import EmailService
    from services.data import DataService
    from services.workflow import WorkflowEngine
    from ai.analyzer import AIAnalyzer
    print('✅ 所有关键模块导入成功')
except ImportError as e:
    print(f'❌ 模块导入失败: {e}')
    exit(1)
"

if errorlevel 1 (
    echo ❌ 模块导入检查失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎯 环境设置和验证完成！
echo ========================================
echo.
echo 系统状态：
echo ✅ 虚拟环境正常
echo ✅ 依赖包已安装
echo ✅ Pylance 验证通过
echo ✅ 关键模块可正常导入
echo.
echo 现在可以开始开发工作了！
echo.
echo 常用命令：
echo   启动 API 服务：python api/main.py
echo   启动 Web 界面：streamlit run ui/main.py
echo   运行测试：pytest tests/
echo.
pause
