import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._zsrc.ZsrcValidator",
        "._zhoverformat.ZhoverformatValidator",
        "._zcalendar.ZcalendarValidator",
        "._z.ZValidator",
        "._ysrc.YsrcValidator",
        "._yhoverformat.YhoverformatValidator",
        "._ycalendar.YcalendarValidator",
        "._y.YValidator",
        "._xsrc.XsrcValidator",
        "._xhoverformat.XhoverformatValidator",
        "._xcalendar.XcalendarValidator",
        "._x.XValidator",
        "._visible.VisibleValidator",
        "._vertexcolorsrc.VertexcolorsrcValidator",
        "._vertexcolor.VertexcolorValidator",
        "._uirevision.UirevisionValidator",
        "._uid.UidValidator",
        "._textsrc.TextsrcValidator",
        "._text.TextValidator",
        "._stream.StreamValidator",
        "._showscale.ShowscaleValidator",
        "._showlegend.ShowlegendValidator",
        "._scene.SceneValidator",
        "._reversescale.ReversescaleValidator",
        "._opacity.OpacityValidator",
        "._name.NameValidator",
        "._metasrc.MetasrcValidator",
        "._meta.MetaValidator",
        "._lightposition.LightpositionValidator",
        "._lighting.LightingValidator",
        "._legendwidth.LegendwidthValidator",
        "._legendrank.LegendrankValidator",
        "._legendgrouptitle.LegendgrouptitleValidator",
        "._legendgroup.LegendgroupValidator",
        "._legend.LegendValidator",
        "._ksrc.KsrcValidator",
        "._k.KValidator",
        "._jsrc.JsrcValidator",
        "._j.JValidator",
        "._isrc.IsrcValidator",
        "._intensitysrc.IntensitysrcValidator",
        "._intensitymode.IntensitymodeValidator",
        "._intensity.IntensityValidator",
        "._idssrc.IdssrcValidator",
        "._ids.IdsValidator",
        "._i.IValidator",
        "._hovertextsrc.HovertextsrcValidator",
        "._hovertext.HovertextValidator",
        "._hovertemplatesrc.HovertemplatesrcValidator",
        "._hovertemplate.HovertemplateValidator",
        "._hoverlabel.HoverlabelValidator",
        "._hoverinfosrc.HoverinfosrcValidator",
        "._hoverinfo.HoverinfoValidator",
        "._flatshading.FlatshadingValidator",
        "._facecolorsrc.FacecolorsrcValidator",
        "._facecolor.FacecolorValidator",
        "._delaunayaxis.DelaunayaxisValidator",
        "._customdatasrc.CustomdatasrcValidator",
        "._customdata.CustomdataValidator",
        "._contour.ContourValidator",
        "._colorscale.ColorscaleValidator",
        "._colorbar.ColorbarValidator",
        "._coloraxis.ColoraxisValidator",
        "._color.ColorValidator",
        "._cmin.CminValidator",
        "._cmid.CmidValidator",
        "._cmax.CmaxValidator",
        "._cauto.CautoValidator",
        "._autocolorscale.AutocolorscaleValidator",
        "._alphahull.AlphahullValidator",
    ],
)
