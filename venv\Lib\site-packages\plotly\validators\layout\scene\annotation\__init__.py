import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._z.ZValidator",
        "._yshift.YshiftValidator",
        "._yanchor.YanchorValidator",
        "._y.YValidator",
        "._xshift.XshiftValidator",
        "._xanchor.XanchorValidator",
        "._x.XValidator",
        "._width.WidthValidator",
        "._visible.VisibleValidator",
        "._valign.ValignValidator",
        "._textangle.TextangleValidator",
        "._text.TextValidator",
        "._templateitemname.TemplateitemnameValidator",
        "._startstandoff.StartstandoffValidator",
        "._startarrowsize.StartarrowsizeValidator",
        "._startarrowhead.StartarrowheadValidator",
        "._standoff.StandoffValidator",
        "._showarrow.ShowarrowValidator",
        "._opacity.OpacityValidator",
        "._name.NameValidator",
        "._hovertext.HovertextValidator",
        "._hoverlabel.HoverlabelValidator",
        "._height.HeightValidator",
        "._font.FontValidator",
        "._captureevents.CaptureeventsValidator",
        "._borderwidth.BorderwidthValidator",
        "._borderpad.BorderpadValidator",
        "._bordercolor.BordercolorValidator",
        "._bgcolor.BgcolorValidator",
        "._ay.AyValidator",
        "._ax.AxValidator",
        "._arrowwidth.ArrowwidthValidator",
        "._arrowsize.ArrowsizeValidator",
        "._arrowside.ArrowsideValidator",
        "._arrowhead.ArrowheadValidator",
        "._arrowcolor.ArrowcolorValidator",
        "._align.AlignValidator",
    ],
)
