#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的核心功能检查

模块描述: 快速检查项目核心功能实现状态
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
"""

import os
import sys

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    exists = os.path.exists(file_path)
    status = "✅" if exists else "❌"
    print(f"{status} {description}: {file_path}")
    return exists

def check_directory_content(dir_path, description, min_files=1):
    """检查目录内容"""
    if not os.path.exists(dir_path):
        print(f"❌ {description}: {dir_path} (目录不存在)")
        return False
    
    files = [f for f in os.listdir(dir_path) if not f.startswith('.') and f != '__pycache__']
    file_count = len(files)
    
    if file_count >= min_files:
        print(f"✅ {description}: {dir_path} ({file_count}个文件)")
        return True
    else:
        print(f"❌ {description}: {dir_path} (只有{file_count}个文件)")
        return False

def check_code_content(file_path, keywords, description):
    """检查代码内容"""
    if not os.path.exists(file_path):
        print(f"❌ {description}: {file_path} (文件不存在)")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().lower()
            
        found_keywords = [kw for kw in keywords if kw.lower() in content]
        
        if len(found_keywords) >= len(keywords) * 0.5:  # 至少找到一半关键词
            print(f"✅ {description}: {file_path} (包含: {', '.join(found_keywords)})")
            return True
        else:
            print(f"⚠️ {description}: {file_path} (缺少关键功能)")
            return False
    except Exception as e:
        print(f"❌ {description}: {file_path} (读取失败: {e})")
        return False

def main():
    """主检查函数"""
    print("🔍 项目核心功能快速检查")
    print("=" * 60)
    
    total_checks = 0
    passed_checks = 0
    
    # 1. 邮件采集功能检查
    print("\n📧 邮件采集功能")
    print("-" * 30)
    
    checks = [
        check_file_exists("email_downloader.py", "邮件下载器"),
        check_directory_content("email_module", "邮件模块", 2),
        check_file_exists("config.py", "邮件配置"),
        check_code_content("email_downloader.py", ["imap", "download", "attachment"], "邮件下载功能")
    ]
    
    email_score = sum(checks)
    total_checks += len(checks)
    passed_checks += email_score
    print(f"邮件采集完成度: {email_score}/{len(checks)} ({email_score/len(checks)*100:.1f}%)")
    
    # 2. AI分析功能检查
    print("\n🧠 AI分析功能")
    print("-" * 30)
    
    checks = [
        check_directory_content("ai", "AI模块", 2),
        check_file_exists("ai/adapter.py", "AI适配器"),
        check_directory_content("prompt_templates", "提示词模板", 1),
        check_code_content("ai/adapter.py", ["openai", "chat", "completion"], "AI调用功能")
    ]
    
    ai_score = sum(checks)
    total_checks += len(checks)
    passed_checks += ai_score
    print(f"AI分析完成度: {ai_score}/{len(checks)} ({ai_score/len(checks)*100:.1f}%)")
    
    # 3. 数据库集成检查
    print("\n🗄️ 数据库集成")
    print("-" * 30)
    
    checks = [
        check_directory_content("db", "数据库模块", 2),
        check_directory_content("domain", "领域模型", 2),
        check_file_exists("config/base.py", "数据库配置"),
        check_code_content("db/orm.py", ["email", "employee", "report"], "数据模型")
    ]
    
    db_score = sum(checks)
    total_checks += len(checks)
    passed_checks += db_score
    print(f"数据库集成完成度: {db_score}/{len(checks)} ({db_score/len(checks)*100:.1f}%)")
    
    # 4. 前端业务功能检查
    print("\n🖥️ 前端业务功能")
    print("-" * 30)
    
    checks = [
        check_directory_content("ui", "UI模块", 2),
        check_directory_content("components", "组件系统", 4),
        check_file_exists("ui/app.py", "主应用"),
        check_code_content("ui/app.py", ["streamlit", "page", "show"], "前端功能")
    ]
    
    frontend_score = sum(checks)
    total_checks += len(checks)
    passed_checks += frontend_score
    print(f"前端业务完成度: {frontend_score}/{len(checks)} ({frontend_score/len(checks)*100:.1f}%)")
    
    # 5. API服务检查
    print("\n🔌 API服务")
    print("-" * 30)
    
    checks = [
        check_directory_content("api", "API模块", 2),
        check_directory_content("services", "服务层", 2),
        check_file_exists("api/main.py", "API主应用"),
        check_code_content("api/main.py", ["fastapi", "router", "endpoint"], "API功能")
    ]
    
    api_score = sum(checks)
    total_checks += len(checks)
    passed_checks += api_score
    print(f"API服务完成度: {api_score}/{len(checks)} ({api_score/len(checks)*100:.1f}%)")
    
    # 总体评估
    print("\n" + "=" * 60)
    overall_completion = (passed_checks / total_checks) * 100
    print(f"📊 总体完成度: {passed_checks}/{total_checks} ({overall_completion:.1f}%)")
    
    if overall_completion >= 80:
        print("🎉 项目核心功能基本完成，可以进入测试阶段")
        status = "ready"
    elif overall_completion >= 60:
        print("⚠️ 项目核心功能大部分完成，需要补充关键功能")
        status = "partial"
    else:
        print("❌ 项目核心功能不完整，需要大量开发工作")
        status = "incomplete"
    
    # 关键缺失功能分析
    print("\n🎯 关键缺失功能分析")
    print("-" * 30)
    
    critical_missing = []
    
    if email_score < 3:
        critical_missing.append("邮件采集功能不完整")
    if ai_score < 3:
        critical_missing.append("AI分析功能不完整")
    if db_score < 3:
        critical_missing.append("数据库集成不完整")
    if frontend_score < 3:
        critical_missing.append("前端业务功能不完整")
    if api_score < 3:
        critical_missing.append("API服务功能不完整")
    
    if critical_missing:
        print("❌ 关键缺失:")
        for missing in critical_missing:
            print(f"   - {missing}")
    else:
        print("✅ 所有核心功能模块基本完整")
    
    # 下一步建议
    print("\n📋 下一步建议")
    print("-" * 30)
    
    if status == "ready":
        print("1. 进行端到端集成测试")
        print("2. 完善错误处理和异常情况")
        print("3. 优化性能和用户体验")
        print("4. 准备生产环境部署")
    elif status == "partial":
        print("1. 优先完成关键缺失功能")
        print("2. 实现核心业务流程")
        print("3. 进行基础功能测试")
        print("4. 完善数据集成")
    else:
        print("1. 按优先级实现核心功能")
        print("2. 先实现邮件采集和AI分析")
        print("3. 再完成数据库集成")
        print("4. 最后完善前端界面")
    
    return status == "ready"

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
