{"include": ["**/*.py"], "exclude": ["**/venv/**", "**/__pycache__/**", "**/node_modules/**", "**/.*/**"], "extraPaths": ["./", "./ai", "./api", "./core", "./domain", "./email_module", "./services", "./ui", "./components", "./infrastructure"], "pythonVersion": "3.12", "pythonPlatform": "Windows", "venvPath": "./", "venv": "venv", "typeCheckingMode": "basic", "useLibraryCodeForTypes": true, "autoImportCompletions": true, "autoSearchPaths": true, "reportMissingImports": "warning", "reportMissingTypeStubs": "none", "reportOptionalMemberAccess": "warning", "reportOptionalSubscript": "warning", "reportOptionalIterable": "warning", "reportOptionalContextManager": "warning", "reportOptionalOperand": "warning", "reportUntypedFunctionDecorator": "none", "reportUnknownParameterType": "none", "reportUnknownVariableType": "none", "reportUnknownMemberType": "none", "reportMissingParameterType": "none", "reportMissingReturnType": "none", "reportUnnecessaryTypeIgnoreComment": "none", "reportConstantRedefinition": "none", "reportIncompatibleMethodOverride": "warning", "reportIncompatibleVariableOverride": "warning", "reportOverlappingOverloads": "warning"}