import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._visible.VisibleValidator",
        "._valuessrc.ValuessrcValidator",
        "._values.ValuesValidator",
        "._uirevision.UirevisionValidator",
        "._uid.UidValidator",
        "._texttemplatesrc.TexttemplatesrcValidator",
        "._texttemplate.TexttemplateValidator",
        "._textsrc.TextsrcValidator",
        "._textinfo.TextinfoValidator",
        "._textfont.TextfontValidator",
        "._text.TextValidator",
        "._stream.StreamValidator",
        "._sort.SortValidator",
        "._rotation.RotationValidator",
        "._root.RootValidator",
        "._parentssrc.ParentssrcValidator",
        "._parents.ParentsValidator",
        "._outsidetextfont.OutsidetextfontValidator",
        "._opacity.OpacityValidator",
        "._name.NameValidator",
        "._metasrc.MetasrcValidator",
        "._meta.MetaValidator",
        "._maxdepth.MaxdepthValidator",
        "._marker.MarkerValidator",
        "._level.LevelValidator",
        "._legendwidth.LegendwidthValidator",
        "._legendrank.LegendrankValidator",
        "._legendgrouptitle.LegendgrouptitleValidator",
        "._legend.LegendValidator",
        "._leaf.LeafValidator",
        "._labelssrc.LabelssrcValidator",
        "._labels.LabelsValidator",
        "._insidetextorientation.InsidetextorientationValidator",
        "._insidetextfont.InsidetextfontValidator",
        "._idssrc.IdssrcValidator",
        "._ids.IdsValidator",
        "._hovertextsrc.HovertextsrcValidator",
        "._hovertext.HovertextValidator",
        "._hovertemplatesrc.HovertemplatesrcValidator",
        "._hovertemplate.HovertemplateValidator",
        "._hoverlabel.HoverlabelValidator",
        "._hoverinfosrc.HoverinfosrcValidator",
        "._hoverinfo.HoverinfoValidator",
        "._domain.DomainValidator",
        "._customdatasrc.CustomdatasrcValidator",
        "._customdata.CustomdataValidator",
        "._count.CountValidator",
        "._branchvalues.BranchvaluesValidator",
    ],
)
