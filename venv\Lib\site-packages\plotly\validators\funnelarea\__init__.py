import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._visible.VisibleValidator",
        "._valuessrc.ValuessrcValidator",
        "._values.ValuesValidator",
        "._uirevision.UirevisionValidator",
        "._uid.UidValidator",
        "._title.TitleValidator",
        "._texttemplatesrc.TexttemplatesrcValidator",
        "._texttemplate.TexttemplateValidator",
        "._textsrc.TextsrcValidator",
        "._textpositionsrc.TextpositionsrcValidator",
        "._textposition.TextpositionValidator",
        "._textinfo.TextinfoValidator",
        "._textfont.TextfontValidator",
        "._text.TextValidator",
        "._stream.StreamValidator",
        "._showlegend.ShowlegendValidator",
        "._scalegroup.ScalegroupValidator",
        "._opacity.OpacityValidator",
        "._name.NameValidator",
        "._metasrc.MetasrcValidator",
        "._meta.MetaValidator",
        "._marker.MarkerValidator",
        "._legendwidth.LegendwidthValidator",
        "._legendrank.LegendrankValidator",
        "._legendgrouptitle.LegendgrouptitleValidator",
        "._legendgroup.LegendgroupValidator",
        "._legend.LegendValidator",
        "._labelssrc.LabelssrcValidator",
        "._labels.LabelsValidator",
        "._label0.Label0Validator",
        "._insidetextfont.InsidetextfontValidator",
        "._idssrc.IdssrcValidator",
        "._ids.IdsValidator",
        "._hovertextsrc.HovertextsrcValidator",
        "._hovertext.HovertextValidator",
        "._hovertemplatesrc.HovertemplatesrcValidator",
        "._hovertemplate.HovertemplateValidator",
        "._hoverlabel.HoverlabelValidator",
        "._hoverinfosrc.HoverinfosrcValidator",
        "._hoverinfo.HoverinfoValidator",
        "._domain.DomainValidator",
        "._dlabel.DlabelValidator",
        "._customdatasrc.CustomdatasrcValidator",
        "._customdata.CustomdataValidator",
        "._baseratio.BaseratioValidator",
        "._aspectratio.AspectratioValidator",
    ],
)
