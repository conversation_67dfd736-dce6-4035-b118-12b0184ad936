{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "python": "./venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: <PERSON><PERSON><PERSON>", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["runserver"], "python": "./venv/Scripts/python.exe", "console": "integratedTerminal", "cwd": "${workspaceFolder}"}]}