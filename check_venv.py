#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
虚拟环境诊断脚本
检查虚拟环境配置和包安装状态
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """检查 Python 版本"""
    print(f"🐍 Python 版本: {sys.version}")
    print(f"📍 Python 路径: {sys.executable}")
    
def check_virtual_env():
    """检查虚拟环境状态"""
    venv_path = Path("venv")
    if venv_path.exists():
        print("✅ 虚拟环境目录存在")
        
        # 检查关键文件
        python_exe = venv_path / "Scripts" / "python.exe"
        if python_exe.exists():
            print("✅ Python 可执行文件存在")
        else:
            print("❌ Python 可执行文件不存在")
            
        pip_exe = venv_path / "Scripts" / "pip.exe"
        if pip_exe.exists():
            print("✅ pip 可执行文件存在")
        else:
            print("❌ pip 可执行文件不存在")
            
        # 检查配置文件
        pyvenv_cfg = venv_path / "pyvenv.cfg"
        if pyvenv_cfg.exists():
            print("✅ pyvenv.cfg 配置文件存在")
            with open(pyvenv_cfg, 'r') as f:
                print("📄 配置内容:")
                print(f.read())
        else:
            print("❌ pyvenv.cfg 配置文件不存在")
    else:
        print("❌ 虚拟环境目录不存在")

def check_packages():
    """检查关键包安装状态"""
    packages = ['psycopg2', 'streamlit', 'plotly', 'scipy', 'fastapi', 'pandas', 'numpy']
    
    print("\n📦 检查关键包安装状态:")
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")

def check_pip_list():
    """检查 pip 包列表"""
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'list'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("\n📋 已安装的包:")
            lines = result.stdout.split('\n')
            for line in lines[:20]:  # 只显示前20个
                if line.strip():
                    print(f"   {line}")
            if len(lines) > 20:
                print(f"   ... 还有 {len(lines) - 20} 个包")
        else:
            print(f"❌ 获取包列表失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 执行 pip list 失败: {e}")

def check_vscode_config():
    """检查 VSCode 配置"""
    vscode_dir = Path(".vscode")
    if vscode_dir.exists():
        print("\n🔧 VSCode 配置:")
        print("✅ .vscode 目录存在")
        
        settings_file = vscode_dir / "settings.json"
        if settings_file.exists():
            print("✅ settings.json 存在")
        else:
            print("❌ settings.json 不存在")
            
        launch_file = vscode_dir / "launch.json"
        if launch_file.exists():
            print("✅ launch.json 存在")
        else:
            print("❌ launch.json 不存在")
    else:
        print("\n❌ .vscode 目录不存在")

def main():
    """主函数"""
    print("🔍 虚拟环境诊断报告")
    print("=" * 50)
    
    check_python_version()
    print()
    
    check_virtual_env()
    print()
    
    check_packages()
    print()
    
    check_pip_list()
    print()
    
    check_vscode_config()
    print()
    
    print("=" * 50)
    print("🎯 诊断完成")

if __name__ == "__main__":
    main()
