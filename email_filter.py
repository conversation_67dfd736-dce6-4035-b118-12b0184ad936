#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
邮件过滤器
根据人员表过滤邮件，只保存在人员表中存在的发件人的邮件和附件
"""

import os
import sys
import logging
import psycopg2
import re
from datetime import datetime, timedelta
from email_module.email_connection import EmailConnection
from config import DB_CONFIG, IMAP_CONFIG, LOG_CONFIG

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_CONFIG["level"]),
    format=LOG_CONFIG["format"],
    handlers=[
        logging.FileHandler("email_filter.log", mode="w"),
        logging.StreamHandler(sys.stdout),
    ],
)
logger = logging.getLogger(__name__)


class EmailFilter:
    """邮件过滤器类"""

    def __init__(self, config=None, save_dir=None):
        """初始化邮件过滤器"""
        self.db_config = config or DB_CONFIG
        self.save_dir = save_dir or os.path.join(
            os.path.dirname(os.path.abspath(__file__)), "attachments"
        )
        self.conn = EmailConnection(config=IMAP_CONFIG)

        # 确保保存目录存在
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
            logger.info(f"创建附件保存目录: {self.save_dir}")

    def is_email_in_staff_table(self, email):
        """检查邮箱是否在人员表中"""
        try:
            # 确保邮箱地址小写
            email = email.lower() if email else ""

            # 如果邮箱为空，返回False
            if not email:
                return False

            # <AUTHOR> <EMAIL>"格式）
            if "<" in email and ">" in email:
                email = email.split("<")[1].split(">")[0].strip()

            # 连接数据库
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            # 查询人员表
            cursor.execute("SELECT COUNT(*) FROM staff_info WHERE email = %s", (email,))
            count = cursor.fetchone()[0]

            # 关闭连接
            cursor.close()
            conn.close()

            return count > 0
        except Exception as e:
            logger.error(f"检查邮箱是否在人员表中时出错: {e}")
            return False

    def extract_email_address(self, email_str):
        """从邮件地址字符串中提取邮箱地址"""
        try:
            # 如果为空，返回空字符串
            if not email_str:
                return ""

            # <AUTHOR> <EMAIL>"格式）
            if "<" in email_str and ">" in email_str:
                email = email_str.split("<")[1].split(">")[0].strip()
            else:
                # 尝试使用正则表达式匹配邮箱地址
                match = re.search(r"[\w\.-]+@[\w\.-]+\.\w+", email_str)
                if match:
                    email = match.group(0)
                else:
                    email = email_str

            # 确保邮箱地址小写
            return email.lower()
        except Exception as e:
            logger.error(f"提取邮箱地址时出错: {e}")
            return ""

    def download_emails(
        self, start_date=None, end_date=None, max_emails=None, mailbox="INBOX"
    ):
        """下载指定时间段的邮件，只保存在人员表中存在的发件人的邮件和附件"""
        try:
            # 连接到邮件服务器
            if not self.conn.connect():
                logger.error("连接邮件服务器失败")
                return False

            # 选择邮箱
            if not self.conn.select_mailbox(mailbox):
                logger.error(f"选择邮箱失败: {mailbox}")
                return False

            # 构建搜索条件
            search_criteria = []

            if start_date:
                if isinstance(start_date, str):
                    start_date = datetime.strptime(start_date, "%Y-%m-%d")
                search_criteria.append(f'(SINCE "{start_date.strftime("%d-%b-%Y")}")')

            if end_date:
                if isinstance(end_date, str):
                    end_date = datetime.strptime(end_date, "%Y-%m-%d")
                search_criteria.append(
                    f'(BEFORE "{(end_date + timedelta(days=1)).strftime("%d-%b-%Y")}")'
                )

            # 如果没有指定日期范围，使用默认天数
            if not search_criteria:
                days = IMAP_CONFIG.get("days", 30)
                date = (datetime.now() - timedelta(days=days)).strftime("%d-%b-%Y")
                search_criteria.append(f'(SINCE "{date}")')

            # 组合搜索条件
            criteria = " ".join(search_criteria)
            logger.info(f"搜索条件: {criteria}")

            # 搜索邮件
            email_ids = self.conn.search_emails(criteria=criteria)
            if not email_ids:
                logger.info("未找到符合条件的邮件")
                return True

            # 限制邮件数量
            max_emails = max_emails or IMAP_CONFIG.get("max_emails", 100)
            if len(email_ids) > max_emails:
                logger.info(f"找到{len(email_ids)}封邮件，限制为{max_emails}封")
                email_ids = email_ids[-max_emails:]

            # 下载邮件和附件
            total_count = len(email_ids)
            processed_count = 0
            saved_count = 0
            skipped_count = 0
            attachment_count = 0

            for email_id in email_ids:
                email_obj = self.conn.get_email(email_id)
                if not email_obj:
                    logger.error(f"获取邮件失败: {email_id}")
                    processed_count += 1
                    continue

                # 提取发件人邮箱地址
                sender_email = self.extract_email_address(email_obj.get("from", ""))

                # 检查发件人是否在人员表中
                if self.is_email_in_staff_table(sender_email):
                    # 保存邮件信息
                    self._save_email_info(email_obj)
                    saved_count += 1

                    # 下载附件
                    if email_obj["attachments"]:
                        for attachment in email_obj["attachments"]:
                            file_path = self._save_attachment(attachment, email_obj)
                            if file_path:
                                attachment_count += 1
                else:
                    logger.info(f"跳过邮件，发件人不在人员表中: {sender_email}")
                    skipped_count += 1

                processed_count += 1
                if processed_count % 10 == 0 or processed_count == total_count:
                    logger.info(
                        f"已处理 {processed_count}/{total_count} 封邮件，保存 {saved_count} 封，跳过 {skipped_count} 封"
                    )

            logger.info(
                f"下载完成，共处理 {processed_count} 封邮件，保存 {saved_count} 封，跳过 {skipped_count} 封，下载 {attachment_count} 个附件"
            )
            return True
        except Exception as e:
            logger.error(f"下载邮件时出错: {e}")
            return False
        finally:
            # 断开连接
            self.conn.disconnect()

    def _save_email_info(self, email_obj):
        """保存邮件信息"""
        try:
            # 创建子目录（按日期）
            email_date = email_obj.get("date", "")
            try:
                # 尝试解析日期
                date_obj = datetime.strptime(email_date, "%a, %d %b %Y %H:%M:%S %z")
                date_str = date_obj.strftime("%Y%m%d")
            except:
                # 如果解析失败，使用当前日期
                date_str = datetime.now().strftime("%Y%m%d")

            save_dir = os.path.join(self.save_dir, date_str)
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            # 生成文件名
            subject = email_obj.get("subject", "").strip()
            if not subject:
                subject = "无主题"

            # 清理文件名
            subject = re.sub(r'[\\/*?:"<>|]', "_", subject)

            # 生成唯一文件名
            sender = email_obj.get("from", "").split("<")[0].strip()
            timestamp = datetime.now().strftime("%H%M%S")
            filename = f"{date_str}_{sender}_{timestamp}_{subject[:50]}.txt"
            file_path = os.path.join(save_dir, filename)

            # 保存邮件信息
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(f"主题: {email_obj.get('subject', '')}\n")
                f.write(f"发件人: {email_obj.get('from', '')}\n")
                f.write(f"收件人: {email_obj.get('to', '')}\n")
                f.write(f"日期: {email_obj.get('date', '')}\n")
                f.write(f"附件数量: {len(email_obj.get('attachments', []))}\n")
                f.write("\n正文:\n")
                f.write(email_obj.get("body", ""))

            logger.info(f"邮件信息已保存: {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"保存邮件信息失败: {e}")
            return None

    def _save_attachment(self, attachment, email_obj):
        """保存附件，避免文件名重复"""
        try:
            filename = attachment["filename"]
            part = attachment["part"]

            # 创建子目录（按日期）
            email_date = email_obj.get("date", "")
            try:
                # 尝试解析日期
                date_obj = datetime.strptime(email_date, "%a, %d %b %Y %H:%M:%S %z")
                date_str = date_obj.strftime("%Y%m%d")
            except:
                # 如果解析失败，使用当前日期
                date_str = datetime.now().strftime("%Y%m%d")

            save_dir = os.path.join(self.save_dir, date_str)
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            # 清理文件名
            filename = re.sub(r'[\\/*?:"<>|]', "_", filename)

            # 分离文件名和扩展名
            name, ext = os.path.splitext(filename)

            # 添加发件人前缀
            sender = email_obj.get("from", "").split("<")[0].strip().replace(" ", "_")
            if sender:
                name = f"{sender}_{name}"

            # 检查文件是否已存在
            file_path = os.path.join(save_dir, f"{name}{ext}")
            counter = 1

            while os.path.exists(file_path):
                # 如果文件已存在，添加计数器
                file_path = os.path.join(save_dir, f"{name}_{counter}{ext}")
                counter += 1

            # 保存附件
            with open(file_path, "wb") as f:
                f.write(part.get_payload(decode=True))

            logger.info(f"附件已保存: {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"保存附件失败: {e}")
            return None


def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description="下载指定时间段的邮件，只保存在人员表中存在的发件人的邮件和附件"
    )
    parser.add_argument("--start", "-s", help="开始日期，格式：YYYY-MM-DD")
    parser.add_argument("--end", "-e", help="结束日期，格式：YYYY-MM-DD")
    parser.add_argument("--max", "-m", type=int, help="最大邮件数量")
    parser.add_argument(
        "--mailbox", "-b", default="INBOX", help="邮箱名称，默认为INBOX"
    )
    parser.add_argument("--dir", "-d", help="附件保存目录")
    args = parser.parse_args()

    # 创建过滤器
    filter = EmailFilter(save_dir=args.dir)

    # 下载邮件
    print(f"开始下载邮件...")
    if args.start:
        print(f"开始日期: {args.start}")
    if args.end:
        print(f"结束日期: {args.end}")
    if args.max:
        print(f"最大邮件数量: {args.max}")
    print(f"邮箱: {args.mailbox}")
    print(f"附件保存目录: {filter.save_dir}")
    print(f"注意: 只保存在人员表中存在的发件人的邮件和附件")

    success = filter.download_emails(
        start_date=args.start,
        end_date=args.end,
        max_emails=args.max,
        mailbox=args.mailbox,
    )

    if success:
        print("邮件下载完成")
    else:
        print("邮件下载失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
