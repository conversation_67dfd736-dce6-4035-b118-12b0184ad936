import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._zsrc.ZsrcValidator",
        "._zhoverformat.ZhoverformatValidator",
        "._z.ZValidator",
        "._ysrc.YsrcValidator",
        "._yhoverformat.YhoverformatValidator",
        "._y.YValidator",
        "._xsrc.XsrcValidator",
        "._xhoverformat.XhoverformatValidator",
        "._x.XValidator",
        "._wsrc.WsrcValidator",
        "._whoverformat.WhoverformatValidator",
        "._w.WValidator",
        "._vsrc.VsrcValidator",
        "._visible.VisibleValidator",
        "._vhoverformat.VhoverformatValidator",
        "._v.VValidator",
        "._usrc.UsrcValidator",
        "._uirevision.UirevisionValidator",
        "._uid.UidValidator",
        "._uhoverformat.UhoverformatValidator",
        "._u.UValidator",
        "._text.TextValidator",
        "._stream.StreamValidator",
        "._starts.StartsValidator",
        "._sizeref.SizerefValidator",
        "._showscale.ShowscaleValidator",
        "._showlegend.ShowlegendValidator",
        "._scene.SceneValidator",
        "._reversescale.ReversescaleValidator",
        "._opacity.OpacityValidator",
        "._name.NameValidator",
        "._metasrc.MetasrcValidator",
        "._meta.MetaValidator",
        "._maxdisplayed.MaxdisplayedValidator",
        "._lightposition.LightpositionValidator",
        "._lighting.LightingValidator",
        "._legendwidth.LegendwidthValidator",
        "._legendrank.LegendrankValidator",
        "._legendgrouptitle.LegendgrouptitleValidator",
        "._legendgroup.LegendgroupValidator",
        "._legend.LegendValidator",
        "._idssrc.IdssrcValidator",
        "._ids.IdsValidator",
        "._hovertext.HovertextValidator",
        "._hovertemplatesrc.HovertemplatesrcValidator",
        "._hovertemplate.HovertemplateValidator",
        "._hoverlabel.HoverlabelValidator",
        "._hoverinfosrc.HoverinfosrcValidator",
        "._hoverinfo.HoverinfoValidator",
        "._customdatasrc.CustomdatasrcValidator",
        "._customdata.CustomdataValidator",
        "._colorscale.ColorscaleValidator",
        "._colorbar.ColorbarValidator",
        "._coloraxis.ColoraxisValidator",
        "._cmin.CminValidator",
        "._cmid.CmidValidator",
        "._cmax.CmaxValidator",
        "._cauto.CautoValidator",
        "._autocolorscale.AutocolorscaleValidator",
    ],
)
