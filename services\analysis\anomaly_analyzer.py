#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
异常检测分析器

模块描述: 专门用于检测工作数据中的异常模式，复用现有代码架构
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: core.interfaces, domain.entities, numpy, scipy
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import numpy as np
from scipy import stats
import logging

from core.interfaces import IAnalyzer
from domain.entities import WeeklyReport, AnalysisResult, TaskComplexity, TaskCategory
from domain.value_objects import ProcessingContext

logger = logging.getLogger(__name__)


class AnomalyAnalyzer(IAnalyzer):
    """
    异常检测分析器
    
    功能：
    - 工作时长异常检测
    - 任务分布异常检测
    - 效率异常检测
    - 行为模式异常检测
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化异常检测分析器
        
        Args:
            config: 配置参数
        """
        self.name = "anomaly_analyzer"
        self.version = "1.0.0"
        self.config = config or {}
        
        # 异常检测阈值
        self.thresholds = {
            'work_hours_min': self.config.get('work_hours_min', 10),
            'work_hours_max': self.config.get('work_hours_max', 60),
            'task_count_min': self.config.get('task_count_min', 1),
            'task_count_max': self.config.get('task_count_max', 20),
            'efficiency_threshold': self.config.get('efficiency_threshold', 0.3),
            'z_score_threshold': self.config.get('z_score_threshold', 2.0)
        }
        
        # 历史数据用于统计分析
        self.historical_data = []
        
    def get_name(self) -> str:
        """获取分析器名称"""
        return self.name
    
    def get_version(self) -> str:
        """获取版本"""
        return self.version
    
    def get_description(self) -> str:
        """获取描述"""
        return "专业异常检测分析器，识别工作数据中的异常模式和行为"
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['weekly_report', 'work_item', 'employee_data']
    
    def initialize(self) -> bool:
        """初始化分析器"""
        try:
            logger.info("异常检测分析器初始化成功")
            return True
        except Exception as e:
            logger.error(f"异常检测分析器初始化失败: {e}")
            return False
    
    def analyze(self, data: WeeklyReport, context: ProcessingContext = None) -> AnalysisResult:
        """
        分析周报数据中的异常
        
        Args:
            data: 周报数据
            context: 处理上下文
            
        Returns:
            AnalysisResult: 异常检测结果
        """
        start_time = datetime.now()
        
        try:
            # 执行多维度异常检测
            anomalies = []
            
            # 1. 工作时长异常检测
            work_hours_anomalies = self._detect_work_hours_anomalies(data)
            anomalies.extend(work_hours_anomalies)
            
            # 2. 任务分布异常检测
            task_distribution_anomalies = self._detect_task_distribution_anomalies(data)
            anomalies.extend(task_distribution_anomalies)
            
            # 3. 效率异常检测
            efficiency_anomalies = self._detect_efficiency_anomalies(data)
            anomalies.extend(efficiency_anomalies)
            
            # 4. 行为模式异常检测
            behavior_anomalies = self._detect_behavior_anomalies(data)
            anomalies.extend(behavior_anomalies)
            
            # 5. 统计异常检测
            statistical_anomalies = self._detect_statistical_anomalies(data)
            anomalies.extend(statistical_anomalies)
            
            # 计算异常严重程度
            severity_analysis = self._analyze_severity(anomalies)
            
            # 生成建议
            recommendations = self._generate_recommendations(anomalies, data)
            
            # 构建结果
            results = {
                'anomalies': anomalies,
                'anomaly_count': len(anomalies),
                'severity_analysis': severity_analysis,
                'recommendations': recommendations,
                'detection_methods': [
                    'work_hours_analysis',
                    'task_distribution_analysis', 
                    'efficiency_analysis',
                    'behavior_pattern_analysis',
                    'statistical_analysis'
                ]
            }
            
            # 计算置信度
            confidence_score = self._calculate_confidence(anomalies, data)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 更新历史数据
            self._update_historical_data(data)
            
            return AnalysisResult(
                result_id=f"anomaly_{data.report_id}_{int(datetime.now().timestamp())}",
                report_id=data.report_id,
                analysis_type=self.name,
                result_data=results,
                confidence_score=confidence_score,
                model_version=self.version,
                processing_time=processing_time
            )
            
        except Exception as e:
            logger.error(f"异常检测分析失败: {e}")
            processing_time = (datetime.now() - start_time).total_seconds()
            return AnalysisResult(
                result_id=f"anomaly_error_{data.report_id}_{int(datetime.now().timestamp())}",
                report_id=data.report_id,
                analysis_type=self.name,
                result_data={'error': str(e)},
                confidence_score=0.0,
                model_version=self.version,
                processing_time=processing_time
            )
    
    def _detect_work_hours_anomalies(self, report: WeeklyReport) -> List[Dict[str, Any]]:
        """检测工作时长异常"""
        anomalies = []
        
        total_hours = sum(item.duration_hours for item in report.work_items)
        
        # 检测总工作时长异常
        if total_hours < self.thresholds['work_hours_min']:
            anomalies.append({
                'type': 'low_work_hours',
                'severity': 'medium',
                'description': f'工作时长过低: {total_hours:.1f}小时 (最低要求: {self.thresholds["work_hours_min"]}小时)',
                'value': total_hours,
                'threshold': self.thresholds['work_hours_min'],
                'category': 'work_hours'
            })
        elif total_hours > self.thresholds['work_hours_max']:
            anomalies.append({
                'type': 'excessive_work_hours',
                'severity': 'high',
                'description': f'工作时长过高: {total_hours:.1f}小时 (建议上限: {self.thresholds["work_hours_max"]}小时)',
                'value': total_hours,
                'threshold': self.thresholds['work_hours_max'],
                'category': 'work_hours'
            })
        
        # 检测单个任务时长异常
        if report.work_items:
            hours_list = [item.duration_hours for item in report.work_items]
            mean_hours = np.mean(hours_list)
            std_hours = np.std(hours_list)
            
            for item in report.work_items:
                if std_hours > 0:
                    z_score = abs(item.duration_hours - mean_hours) / std_hours
                    if z_score > self.thresholds['z_score_threshold']:
                        anomalies.append({
                            'type': 'task_duration_outlier',
                            'severity': 'low',
                            'description': f'任务"{item.title}"时长异常: {item.duration_hours:.1f}小时 (Z-score: {z_score:.2f})',
                            'value': item.duration_hours,
                            'z_score': z_score,
                            'task_id': item.work_item_id,
                            'category': 'task_duration'
                        })
        
        return anomalies
    
    def _detect_task_distribution_anomalies(self, report: WeeklyReport) -> List[Dict[str, Any]]:
        """检测任务分布异常"""
        anomalies = []
        
        task_count = len(report.work_items)
        
        # 检测任务数量异常
        if task_count < self.thresholds['task_count_min']:
            anomalies.append({
                'type': 'low_task_count',
                'severity': 'medium',
                'description': f'任务数量过少: {task_count}个 (建议最少: {self.thresholds["task_count_min"]}个)',
                'value': task_count,
                'threshold': self.thresholds['task_count_min'],
                'category': 'task_distribution'
            })
        elif task_count > self.thresholds['task_count_max']:
            anomalies.append({
                'type': 'excessive_task_count',
                'severity': 'medium',
                'description': f'任务数量过多: {task_count}个 (建议上限: {self.thresholds["task_count_max"]}个)',
                'value': task_count,
                'threshold': self.thresholds['task_count_max'],
                'category': 'task_distribution'
            })
        
        # 检测复杂度分布异常
        if report.work_items:
            complexity_counts = {
                TaskComplexity.LOW: 0,
                TaskComplexity.MEDIUM: 0,
                TaskComplexity.HIGH: 0
            }
            
            for item in report.work_items:
                complexity_counts[item.complexity] += 1
            
            total_tasks = len(report.work_items)
            high_complexity_ratio = complexity_counts[TaskComplexity.HIGH] / total_tasks
            low_complexity_ratio = complexity_counts[TaskComplexity.LOW] / total_tasks
            
            # 检测高复杂度任务过多
            if high_complexity_ratio > 0.7:
                anomalies.append({
                    'type': 'excessive_high_complexity',
                    'severity': 'medium',
                    'description': f'高复杂度任务占比过高: {high_complexity_ratio:.1%}',
                    'value': high_complexity_ratio,
                    'category': 'complexity_distribution'
                })
            
            # 检测低复杂度任务过多
            if low_complexity_ratio > 0.8:
                anomalies.append({
                    'type': 'excessive_low_complexity',
                    'severity': 'low',
                    'description': f'低复杂度任务占比过高: {low_complexity_ratio:.1%}',
                    'value': low_complexity_ratio,
                    'category': 'complexity_distribution'
                })
        
        return anomalies
    
    def _detect_efficiency_anomalies(self, report: WeeklyReport) -> List[Dict[str, Any]]:
        """检测效率异常"""
        anomalies = []
        
        if not report.work_items:
            return anomalies
        
        # 计算效率指标
        total_hours = sum(item.duration_hours for item in report.work_items)
        task_count = len(report.work_items)
        avg_hours_per_task = total_hours / task_count
        
        # 检测平均任务时长异常
        if avg_hours_per_task > 10:  # 平均每个任务超过10小时
            anomalies.append({
                'type': 'low_task_efficiency',
                'severity': 'medium',
                'description': f'平均任务耗时过长: {avg_hours_per_task:.1f}小时/任务',
                'value': avg_hours_per_task,
                'category': 'efficiency'
            })
        
        # 检测高复杂度任务效率
        high_complexity_items = [item for item in report.work_items if item.complexity == TaskComplexity.HIGH]
        if high_complexity_items:
            high_complexity_hours = sum(item.duration_hours for item in high_complexity_items)
            high_complexity_ratio = high_complexity_hours / total_hours
            
            if high_complexity_ratio < self.thresholds['efficiency_threshold']:
                anomalies.append({
                    'type': 'low_high_complexity_focus',
                    'severity': 'low',
                    'description': f'高复杂度任务时间占比过低: {high_complexity_ratio:.1%}',
                    'value': high_complexity_ratio,
                    'category': 'efficiency'
                })
        
        return anomalies
    
    def _detect_behavior_anomalies(self, report: WeeklyReport) -> List[Dict[str, Any]]:
        """检测行为模式异常"""
        anomalies = []
        
        if not report.work_items:
            return anomalies
        
        # 分析任务类型分布
        category_hours = {}
        for item in report.work_items:
            category = item.category.value
            category_hours[category] = category_hours.get(category, 0) + item.duration_hours
        
        total_hours = sum(category_hours.values())
        
        # 检测单一类型任务过度集中
        for category, hours in category_hours.items():
            ratio = hours / total_hours
            if ratio > 0.8:  # 单一类型任务占比超过80%
                anomalies.append({
                    'type': 'task_type_concentration',
                    'severity': 'low',
                    'description': f'任务类型过度集中于"{category}": {ratio:.1%}',
                    'value': ratio,
                    'category': 'behavior_pattern'
                })
        
        return anomalies
    
    def _detect_statistical_anomalies(self, report: WeeklyReport) -> List[Dict[str, Any]]:
        """检测统计异常"""
        anomalies = []
        
        if len(self.historical_data) < 3:  # 需要足够的历史数据
            return anomalies
        
        # 与历史数据比较
        current_total_hours = sum(item.duration_hours for item in report.work_items)
        historical_hours = [data['total_hours'] for data in self.historical_data]
        
        if historical_hours:
            mean_hours = np.mean(historical_hours)
            std_hours = np.std(historical_hours)
            
            if std_hours > 0:
                z_score = abs(current_total_hours - mean_hours) / std_hours
                if z_score > self.thresholds['z_score_threshold']:
                    anomalies.append({
                        'type': 'statistical_outlier',
                        'severity': 'medium',
                        'description': f'工作时长与历史模式差异较大 (Z-score: {z_score:.2f})',
                        'value': current_total_hours,
                        'z_score': z_score,
                        'historical_mean': mean_hours,
                        'category': 'statistical'
                    })
        
        return anomalies
    
    def _analyze_severity(self, anomalies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析异常严重程度"""
        severity_counts = {'low': 0, 'medium': 0, 'high': 0}
        
        for anomaly in anomalies:
            severity = anomaly.get('severity', 'low')
            severity_counts[severity] += 1
        
        total_anomalies = len(anomalies)
        overall_severity = 'low'
        
        if severity_counts['high'] > 0:
            overall_severity = 'high'
        elif severity_counts['medium'] > 0:
            overall_severity = 'medium'
        
        return {
            'overall_severity': overall_severity,
            'severity_distribution': severity_counts,
            'total_anomalies': total_anomalies,
            'risk_score': severity_counts['high'] * 3 + severity_counts['medium'] * 2 + severity_counts['low'] * 1
        }
    
    def _generate_recommendations(self, anomalies: List[Dict[str, Any]], report: WeeklyReport) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于异常类型生成建议
        anomaly_types = set(anomaly['type'] for anomaly in anomalies)
        
        if 'low_work_hours' in anomaly_types:
            recommendations.append("建议增加工作投入时间，确保完成既定目标")
        
        if 'excessive_work_hours' in anomaly_types:
            recommendations.append("工作时长过长，建议优化时间管理，注意工作生活平衡")
        
        if 'low_task_efficiency' in anomaly_types:
            recommendations.append("任务效率偏低，建议分解大任务，提高执行效率")
        
        if 'task_type_concentration' in anomaly_types:
            recommendations.append("任务类型过于单一，建议增加工作内容的多样性")
        
        if 'excessive_high_complexity' in anomaly_types:
            recommendations.append("高复杂度任务过多，建议合理分配难易程度")
        
        # 通用建议
        if len(anomalies) > 3:
            recommendations.append("检测到多项异常，建议全面审视工作安排和执行方式")
        
        return recommendations
    
    def _calculate_confidence(self, anomalies: List[Dict[str, Any]], report: WeeklyReport) -> float:
        """计算检测置信度"""
        base_confidence = 0.8
        
        # 基于数据完整性调整置信度
        if not report.work_items:
            return 0.2
        
        # 基于异常数量调整置信度
        anomaly_count = len(anomalies)
        if anomaly_count == 0:
            return 0.9  # 没有异常，高置信度
        elif anomaly_count <= 2:
            return 0.85  # 少量异常，较高置信度
        else:
            return max(0.6, base_confidence - (anomaly_count - 2) * 0.05)
    
    def _update_historical_data(self, report: WeeklyReport) -> None:
        """更新历史数据"""
        data_point = {
            'timestamp': datetime.now(),
            'total_hours': sum(item.duration_hours for item in report.work_items),
            'task_count': len(report.work_items),
            'report_id': report.report_id
        }
        
        self.historical_data.append(data_point)
        
        # 保持历史数据在合理范围内
        if len(self.historical_data) > 50:
            self.historical_data = self.historical_data[-30:]
