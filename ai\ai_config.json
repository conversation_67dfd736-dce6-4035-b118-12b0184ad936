{"openai": {"model": "qwen-turbo-2025-04-28", "api_key": "sk-e7494146b9d34192961dd4dad8489dd4", "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1", "max_tokens": 8000, "temperature": 0.7, "max_context_length": 64000, "enable_thinking": false, "timeout": 120, "thinking_budget": null, "models": ["qwen-turbo-2025-04-28", "qwen3-235b-a22b", "qwen-max", "qwen-plus", "qwen-turbo", "qwen-long"]}, "alibaba": {"model": "qwen-turbo-2025-04-28", "api_key": "sk-e7494146b9d34192961dd4dad8489dd4", "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", "max_tokens": 8000, "temperature": 0.7, "max_context_length": 64000, "models": ["qwen-turbo-2025-04-28", "qwen3-235b-a22b", "qwen-max", "qwen-plus", "qwen-turbo", "qwen-long"]}, "deepseek": {"model": "deepseek-chat", "api_key": "***********************************", "api_url": "https://api.deepseek.com/v1/chat/completions", "base_url": "https://api.deepseek.com/v1", "max_tokens": 8000, "temperature": 0.7, "max_context_length": 64000, "timeout": 120, "enable_thinking": false, "models": ["deepseek-chat", "deepseek-reasoner", "deepseek-coder"]}, "ollama": {"model": "qwq", "base_url": "http://112.48.22.44:38082", "timeout": 120, "max_tokens": 8000, "temperature": 0.7, "max_context_length": 32768, "models": ["gemma3:27b", "llama2:13b", "mistral:7b"]}, "deep_analysis": {"model": "qwen3-235b-a22b", "api_key": "sk-e7494146b9d34192961dd4dad8489dd4", "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", "max_tokens": 8000, "temperature": 0.7, "max_context_length": 64000, "enable_thinking": true}, "embedding_alibaba": {"model": "text-embedding-v1", "api_key": "sk-e7494146b9d34192961dd4dad8489dd4", "api_url": "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding", "max_tokens": 2048, "temperature": 0.0, "max_context_length": 2048, "timeout": 60, "models": ["text-embedding-v1", "text-embedding-v2"]}, "embedding_openai": {"model": "text-embedding-ada-002", "api_key": "your-openai-api-key", "api_url": "https://api.openai.com/v1/embeddings", "max_tokens": 8191, "temperature": 0.0, "max_context_length": 8191, "timeout": 60, "models": ["text-embedding-ada-002", "text-embedding-3-small", "text-embedding-3-large"]}, "vision_alibaba": {"model": "qwen-vl-plus", "api_key": "sk-e7494146b9d34192961dd4dad8489dd4", "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", "max_tokens": 2000, "temperature": 0.7, "max_context_length": 32000, "timeout": 120, "models": ["qwen-vl-plus", "qwen-vl-max"]}, "disabled_providers": []}