#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
机器学习分析器

模块描述: 基于机器学习的数据分析器，支持多种ML算法
作者: 开发团队
创建时间: 2025-01-27
版本: 1.0.0
依赖: core.interfaces, domain.entities, sklearn, numpy
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import numpy as np
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score
import joblib
import os

from core.interfaces import IAnalyzer
from domain.entities import WeeklyReport, AnalysisResult, TaskComplexity, TaskCategory
from domain.value_objects import ProcessingContext


class MLAnalyzer(IAnalyzer):
    """
    机器学习分析器

    功能：
    - 工作效率预测
    - 任务复杂度分类
    - 工作模式识别
    - 异常检测
    """

    def __init__(self, model_path: str = None):
        """
        初始化ML分析器

        Args:
            model_path: 模型保存路径
        """
        self.name = "ml_analyzer"
        self.version = "1.0.0"
        self.model_path = model_path or "models/ml_analyzer"

        # 模型组件
        self.efficiency_model = None
        self.complexity_model = None
        self.anomaly_model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}

        # 模型状态
        self.is_trained = False
        self.feature_names = []

        # 确保模型目录存在
        os.makedirs(self.model_path, exist_ok=True)

    def get_name(self) -> str:
        """获取分析器名称"""
        return self.name

    def get_version(self) -> str:
        """获取版本"""
        return self.version

    def get_description(self) -> str:
        """获取描述"""
        return "基于机器学习的智能分析器，支持效率预测、复杂度分类和异常检测"

    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ["weekly_report", "work_item", "employee_data"]

    def validate_input(self, data: Any) -> bool:
        """
        验证输入数据

        Args:
            data: 输入数据

        Returns:
            bool: 数据是否有效
        """
        if not isinstance(data, WeeklyReport):
            return False

        # 检查必要字段
        if not hasattr(data, "report_id") or not data.report_id:
            return False

        if not hasattr(data, "work_items") or not isinstance(data.work_items, list):
            return False

        # 检查工作项数据
        for item in data.work_items:
            if not hasattr(item, "duration_hours") or not isinstance(
                item.duration_hours, (int, float)
            ):
                return False
            if not hasattr(item, "complexity") or item.complexity is None:
                return False
            if not hasattr(item, "category") or item.category is None:
                return False

        return True

    def initialize(self) -> bool:
        """初始化分析器"""
        try:
            # 尝试加载已训练的模型
            self._load_models()
            return True
        except Exception as e:
            print(f"ML分析器初始化失败: {e}")
            return False

    def analyze(
        self, data: WeeklyReport, context: ProcessingContext = None
    ) -> AnalysisResult:
        """
        分析周报数据

        Args:
            data: 周报数据
            context: 处理上下文

        Returns:
            AnalysisResult: 分析结果
        """
        start_time = datetime.now()

        try:
            # 提取特征
            features = self._extract_features(data)

            # 执行各种ML分析
            results = {}

            # 1. 工作效率分析
            if self.efficiency_model is not None:
                efficiency_score = self._predict_efficiency(features)
                results["efficiency"] = {
                    "score": float(efficiency_score),
                    "level": self._categorize_efficiency(efficiency_score),
                    "confidence": 0.85,
                }

            # 2. 任务复杂度分析
            if self.complexity_model is not None:
                complexity_analysis = self._analyze_complexity(data.work_items)
                results["complexity_analysis"] = complexity_analysis

            # 3. 异常检测
            if self.anomaly_model is not None:
                anomalies = self._detect_anomalies(features)
                results["anomalies"] = anomalies

            # 4. 工作模式识别
            work_patterns = self._identify_work_patterns(data)
            results["work_patterns"] = work_patterns

            # 5. 预测性分析
            predictions = self._generate_predictions(features, data)
            results["predictions"] = predictions

            # 计算整体置信度
            confidence_score = self._calculate_confidence(results)

            processing_time = (datetime.now() - start_time).total_seconds()

            return AnalysisResult(
                result_id=f"ml_{data.report_id}_{int(datetime.now().timestamp())}",
                report_id=data.report_id,
                analysis_type=self.name,
                result_data=results,
                confidence_score=confidence_score,
                model_version=self.version,
                processing_time=processing_time,
            )

        except Exception as e:
            # 返回错误结果
            processing_time = (datetime.now() - start_time).total_seconds()
            return AnalysisResult(
                result_id=f"ml_error_{data.report_id}_{int(datetime.now().timestamp())}",
                report_id=data.report_id,
                analysis_type=self.name,
                result_data={"error": str(e)},
                confidence_score=0.0,
                model_version=self.version,
                processing_time=processing_time,
            )

    def train_models(self, training_data: List[WeeklyReport]) -> bool:
        """
        训练ML模型

        Args:
            training_data: 训练数据

        Returns:
            bool: 训练是否成功
        """
        if not training_data:
            return False

        try:
            # 提取特征和标签
            features_list = []
            efficiency_labels = []
            complexity_labels = []

            for report in training_data:
                features = self._extract_features(report)
                features_list.append(features)

                # 计算效率标签（基于工作时长和完成度）
                efficiency = self._calculate_efficiency_label(report)
                efficiency_labels.append(efficiency)

                # 复杂度标签（基于任务复杂度分布）
                complexity = self._calculate_complexity_label(report)
                complexity_labels.append(complexity)

            # 转换为numpy数组
            X = np.array(features_list)
            y_efficiency = np.array(efficiency_labels)
            y_complexity = np.array(complexity_labels)

            # 标准化特征
            X_scaled = self.scaler.fit_transform(X)

            # 训练效率预测模型
            self.efficiency_model = RandomForestClassifier(
                n_estimators=100, random_state=42
            )
            self.efficiency_model.fit(X_scaled, y_efficiency)

            # 训练复杂度分类模型
            self.complexity_model = RandomForestClassifier(
                n_estimators=100, random_state=42
            )
            self.complexity_model.fit(X_scaled, y_complexity)

            # 训练异常检测模型
            self.anomaly_model = IsolationForest(contamination=0.1, random_state=42)
            self.anomaly_model.fit(X_scaled)

            self.is_trained = True

            # 保存模型
            self._save_models()

            return True

        except Exception as e:
            print(f"模型训练失败: {e}")
            return False

    def _extract_features(self, report: WeeklyReport) -> List[float]:
        """提取特征向量"""
        features = []

        # 基本统计特征
        total_hours = sum(item.duration_hours for item in report.work_items)
        work_item_count = len(report.work_items)
        avg_hours_per_item = total_hours / work_item_count if work_item_count > 0 else 0

        features.extend([total_hours, work_item_count, avg_hours_per_item])

        # 复杂度分布特征
        complexity_counts = {
            TaskComplexity.LOW: 0,
            TaskComplexity.MEDIUM: 0,
            TaskComplexity.HIGH: 0,
        }
        for item in report.work_items:
            complexity_counts[item.complexity] += 1

        features.extend(
            [
                complexity_counts[TaskComplexity.LOW],
                complexity_counts[TaskComplexity.MEDIUM],
                complexity_counts[TaskComplexity.HIGH],
            ]
        )

        # 类别分布特征
        category_counts = {}
        for item in report.work_items:
            category = item.category.value
            category_counts[category] = category_counts.get(category, 0) + 1

        # 标准化类别特征（取前5个最常见的类别）
        common_categories = ["开发", "测试", "文档", "会议", "其他"]
        for category in common_categories:
            features.append(category_counts.get(category, 0))

        # 时间分布特征
        if report.work_items:
            hours_list = [item.duration_hours for item in report.work_items]
            features.extend(
                [
                    np.mean(hours_list),
                    np.std(hours_list),
                    np.min(hours_list),
                    np.max(hours_list),
                ]
            )
        else:
            features.extend([0, 0, 0, 0])

        return features

    def _predict_efficiency(self, features: List[float]) -> float:
        """预测工作效率"""
        if self.efficiency_model is None:
            return 0.5  # 默认中等效率

        features_scaled = self.scaler.transform([features])
        prediction = self.efficiency_model.predict_proba(features_scaled)[0]

        # 返回高效率的概率
        return prediction[1] if len(prediction) > 1 else 0.5

    def _analyze_complexity(self, work_items) -> Dict[str, Any]:
        """分析任务复杂度"""
        if not work_items:
            return {"error": "无工作项数据"}

        complexity_dist = {"low": 0, "medium": 0, "high": 0}

        total_hours_by_complexity = {"low": 0, "medium": 0, "high": 0}

        for item in work_items:
            complexity_key = item.complexity.value.lower()
            complexity_dist[complexity_key] += 1
            total_hours_by_complexity[complexity_key] += item.duration_hours

        total_items = len(work_items)
        total_hours = sum(total_hours_by_complexity.values())

        return {
            "distribution": {
                k: {"count": v, "percentage": v / total_items * 100}
                for k, v in complexity_dist.items()
            },
            "hours_distribution": {
                k: {
                    "hours": v,
                    "percentage": v / total_hours * 100 if total_hours > 0 else 0,
                }
                for k, v in total_hours_by_complexity.items()
            },
            "complexity_score": self._calculate_complexity_score(work_items),
        }

    def _detect_anomalies(self, features: List[float]) -> List[Dict[str, Any]]:
        """检测异常"""
        if self.anomaly_model is None:
            return []

        features_scaled = self.scaler.transform([features])
        anomaly_score = self.anomaly_model.decision_function(features_scaled)[0]
        is_anomaly = self.anomaly_model.predict(features_scaled)[0] == -1

        anomalies = []
        if is_anomaly:
            anomalies.append(
                {
                    "type": "statistical_anomaly",
                    "severity": "medium" if anomaly_score < -0.5 else "low",
                    "score": float(anomaly_score),
                    "description": "检测到统计异常模式",
                }
            )

        return anomalies

    def _identify_work_patterns(self, report: WeeklyReport) -> Dict[str, Any]:
        """识别工作模式"""
        if not report.work_items:
            return {}

        # 分析工作时间分布
        hours_by_day = {}  # 这里需要根据实际数据结构调整

        # 分析任务类型偏好
        category_hours = {}
        for item in report.work_items:
            category = item.category.value
            category_hours[category] = (
                category_hours.get(category, 0) + item.duration_hours
            )

        # 识别主要工作模式
        total_hours = sum(category_hours.values())
        primary_category = (
            max(category_hours.items(), key=lambda x: x[1])[0]
            if category_hours
            else None
        )

        return {
            "primary_focus": primary_category,
            "category_distribution": {
                k: v / total_hours * 100 if total_hours > 0 else 0
                for k, v in category_hours.items()
            },
            "work_intensity": (
                "high" if total_hours > 45 else "medium" if total_hours > 30 else "low"
            ),
            "task_diversity": len(set(item.category for item in report.work_items)),
        }

    def _generate_predictions(
        self, features: List[float], report: WeeklyReport
    ) -> Dict[str, Any]:
        """生成预测性分析"""
        predictions = {}

        # 下周工作负载预测
        current_hours = sum(item.duration_hours for item in report.work_items)
        predicted_hours = current_hours * 1.05  # 简单的线性预测

        predictions["next_week_workload"] = {
            "predicted_hours": predicted_hours,
            "confidence": 0.7,
            "trend": "increasing" if predicted_hours > current_hours else "stable",
        }

        # 效率改进建议
        efficiency_score = self._predict_efficiency(features)
        if efficiency_score < 0.6:
            predictions["improvement_suggestions"] = [
                "考虑减少低价值任务的时间投入",
                "增加高复杂度任务的专注时间",
                "优化任务分配和时间管理",
            ]

        return predictions

    def _calculate_confidence(self, results: Dict[str, Any]) -> float:
        """计算整体置信度"""
        if not results:
            return 0.0

        # 基于各个分析结果的置信度计算整体置信度
        confidences = []

        if "efficiency" in results:
            confidences.append(results["efficiency"].get("confidence", 0.5))

        if "anomalies" in results:
            # 异常检测的置信度基于检测到的异常数量
            anomaly_count = len(results["anomalies"])
            confidences.append(0.9 if anomaly_count == 0 else 0.7)

        # 默认置信度
        if not confidences:
            confidences.append(0.6)

        return sum(confidences) / len(confidences)

    def _calculate_efficiency_label(self, report: WeeklyReport) -> int:
        """计算效率标签（用于训练）"""
        total_hours = sum(item.duration_hours for item in report.work_items)
        high_complexity_hours = sum(
            item.duration_hours
            for item in report.work_items
            if item.complexity == TaskComplexity.HIGH
        )

        # 简单的效率计算：高复杂度任务占比高且总时长合理
        efficiency_ratio = high_complexity_hours / total_hours if total_hours > 0 else 0

        return 1 if efficiency_ratio > 0.3 and 30 <= total_hours <= 50 else 0

    def _calculate_complexity_label(self, report: WeeklyReport) -> int:
        """计算复杂度标签（用于训练）"""
        if not report.work_items:
            return 0

        high_complexity_count = sum(
            1 for item in report.work_items if item.complexity == TaskComplexity.HIGH
        )

        return 1 if high_complexity_count >= len(report.work_items) * 0.3 else 0

    def _calculate_complexity_score(self, work_items) -> float:
        """计算复杂度分数"""
        if not work_items:
            return 0.0

        complexity_weights = {
            TaskComplexity.LOW: 1.0,
            TaskComplexity.MEDIUM: 2.0,
            TaskComplexity.HIGH: 3.0,
        }

        total_score = sum(complexity_weights[item.complexity] for item in work_items)
        max_possible_score = len(work_items) * 3.0

        return total_score / max_possible_score if max_possible_score > 0 else 0.0

    def _categorize_efficiency(self, efficiency_score: float) -> str:
        """分类效率等级"""
        if efficiency_score >= 0.8:
            return "high"
        elif efficiency_score >= 0.6:
            return "medium"
        else:
            return "low"

    def _save_models(self) -> None:
        """保存模型"""
        try:
            if self.efficiency_model:
                joblib.dump(
                    self.efficiency_model,
                    os.path.join(self.model_path, "efficiency_model.pkl"),
                )

            if self.complexity_model:
                joblib.dump(
                    self.complexity_model,
                    os.path.join(self.model_path, "complexity_model.pkl"),
                )

            if self.anomaly_model:
                joblib.dump(
                    self.anomaly_model,
                    os.path.join(self.model_path, "anomaly_model.pkl"),
                )

            joblib.dump(self.scaler, os.path.join(self.model_path, "scaler.pkl"))

        except Exception as e:
            print(f"模型保存失败: {e}")

    def _load_models(self) -> None:
        """加载模型"""
        try:
            efficiency_path = os.path.join(self.model_path, "efficiency_model.pkl")
            if os.path.exists(efficiency_path):
                self.efficiency_model = joblib.load(efficiency_path)

            complexity_path = os.path.join(self.model_path, "complexity_model.pkl")
            if os.path.exists(complexity_path):
                self.complexity_model = joblib.load(complexity_path)

            anomaly_path = os.path.join(self.model_path, "anomaly_model.pkl")
            if os.path.exists(anomaly_path):
                self.anomaly_model = joblib.load(anomaly_path)

            scaler_path = os.path.join(self.model_path, "scaler.pkl")
            if os.path.exists(scaler_path):
                self.scaler = joblib.load(scaler_path)
                self.is_trained = True

        except Exception as e:
            print(f"模型加载失败: {e}")

    def cleanup(self) -> None:
        """清理资源"""
        self.efficiency_model = None
        self.complexity_model = None
        self.anomaly_model = None
        self.scaler = StandardScaler()
        self.is_trained = False
