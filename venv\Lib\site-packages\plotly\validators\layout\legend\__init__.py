import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._yref.YrefValidator",
        "._yanchor.YanchorValidator",
        "._y.YValidator",
        "._xref.XrefValidator",
        "._xanchor.XanchorValidator",
        "._x.XValidator",
        "._visible.VisibleValidator",
        "._valign.ValignValidator",
        "._uirevision.UirevisionValidator",
        "._traceorder.TraceorderValidator",
        "._tracegroupgap.TracegroupgapValidator",
        "._title.TitleValidator",
        "._orientation.OrientationValidator",
        "._itemwidth.ItemwidthValidator",
        "._itemsizing.ItemsizingValidator",
        "._itemdoubleclick.ItemdoubleclickValidator",
        "._itemclick.ItemclickValidator",
        "._indentation.IndentationValidator",
        "._grouptitlefont.GrouptitlefontValidator",
        "._groupclick.GroupclickValidator",
        "._font.FontValidator",
        "._entrywidthmode.EntrywidthmodeValidator",
        "._entrywidth.EntrywidthValidator",
        "._borderwidth.BorderwidthValidator",
        "._bordercolor.BordercolorValidator",
        "._bgcolor.BgcolorValidator",
    ],
)
